#ifndef __KEY_MENU_H
#define __KEY_MENU_H

#include "main.h"
#include "tim.h"
#include "gpio.h"
#include <stdint.h>
#include <string.h>

/* 按键状态枚举 */
typedef enum {
    KEY_STATE_RELEASED = 0,    // 未按下
    KEY_STATE_PRESSED,         // 刚按下
    KEY_STATE_HOLDING,         // 持续按下
    KEY_STATE_REPEAT           // 重复触发
} KeyState_t;

/* 按键类型枚举 */
typedef enum {
    KEY_UP = 0,
    KEY_DOWN,
    KEY_ENTER,
    KEY_BACK,
    KEY_COUNT
} KeyType_t;

/* 菜单状态枚举 */
typedef enum {
    MENU_STATE_WEATHER_DISPLAY = 0,    // 天气显示模式
    MENU_STATE_MAIN,                   // 主菜单
    MENU_STATE_CITY_SETTING,           // 城市设置
    MENU_STATE_CITY_QUICK,             // 快速选择城市
    MENU_STATE_CITY_INPUT,             // 自定义输入城市
    MENU_STATE_SYSTEM_INFO,            // 系统信息
    MENU_STATE_CONFIRM                 // 确认对话框
} MenuState_t;

/* 按键信息结构体 */
typedef struct {
    GPIO_TypeDef* port;
    uint16_t pin;
    KeyState_t state;
    uint32_t press_time;
    uint32_t release_time;
    uint8_t debounce_flag;
} KeyInfo_t;

/* 城市信息结构体 */
typedef struct {
    char display_name[32];    // 显示名称（中文）
    char api_name[32];        // API名称（英文）
} CityInfo_t;

/* 用户设置结构体 */
typedef struct {
    uint32_t magic;           // 魔数验证：0x12345678
    char current_city[32];    // 当前选择的城市
    uint8_t city_index;       // 预设城市索引
    uint8_t is_custom;        // 是否为自定义城市
    uint32_t checksum;        // 校验和
} UserSettings_t;

/* 菜单项结构体 */
typedef struct {
    char text[32];           // 菜单项文本
    MenuState_t next_state;  // 下一个状态
} MenuItem_t;

/* Flash存储地址定义 */
#define USER_SETTINGS_FLASH_ADDR    0x0801F800  // Flash最后2KB
#define USER_SETTINGS_MAGIC         0x12345678

/* 按键防抖时间定义 */
#define KEY_DEBOUNCE_TIME          20    // 20ms
#define KEY_REPEAT_TIME            500   // 500ms后开始重复
#define KEY_REPEAT_INTERVAL        100   // 重复间隔100ms

/* 菜单超时时间定义 */
#define MENU_TIMEOUT_SECONDS       30    // 30秒无操作返回天气显示

/* 全局变量声明 */
extern KeyInfo_t g_keys[KEY_COUNT];
extern MenuState_t g_current_menu_state;
extern uint8_t g_menu_cursor;
extern uint8_t g_menu_timeout_counter;
extern uint8_t g_debounce_timer_active;
extern UserSettings_t g_user_settings;

/* 预设城市列表 */
extern const CityInfo_t PRESET_CITIES[];
extern const uint8_t PRESET_CITIES_COUNT;

/* 函数声明 */

/* 按键处理函数 */
void KeyMenu_Init(void);
void KeyMenu_Process(void);
KeyState_t KeyMenu_GetKeyState(KeyType_t key);
void KeyMenu_HandleKeyPress(KeyType_t key);

/* 菜单处理函数 */
void Menu_Display(void);
void Menu_HandleInput(KeyType_t key);
void Menu_ChangeState(MenuState_t new_state);
void Menu_ResetTimeout(void);
void Menu_ForceUpdate(void);

/* 具体菜单处理函数 */
void Menu_ShowWeatherDisplay(void);
void Menu_ShowMainMenu(void);
void Menu_ShowCitySetting(void);
void Menu_ShowCityQuick(void);
void Menu_ShowCityInput(void);
void Menu_ShowSystemInfo(void);
void Menu_ShowConfirm(const char* message);

/* Flash存储函数 */
uint8_t Flash_SaveUserSettings(void);
uint8_t Flash_LoadUserSettings(void);
uint32_t Flash_CalculateChecksum(const UserSettings_t* settings);

/* 城市管理函数 */
void City_SetCurrent(const char* city_name, uint8_t is_custom, uint8_t city_index);
const char* City_GetCurrent(void);
void City_TriggerWeatherUpdate(void);

/* 定时器回调函数 */
void TIM1_DebounceCallback(void);
void TIM3_TimeoutCallback(void);

/* EXTI回调函数 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin);

#endif /* __KEY_MENU_H */
