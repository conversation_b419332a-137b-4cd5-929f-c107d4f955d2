# 按键菜单系统使用说明

## 🎯 **功能概述**

本系统实现了完整的按键菜单功能，允许用户通过物理按键选择不同城市的天气信息，完全解决了Web服务器冲突问题。

## 🔧 **硬件连接**

### **按键连接**：
```
PA0 ── 10kΩ上拉 ── 3.3V
    │
    └── KEY_UP ── GND

PA1 ── 10kΩ上拉 ── 3.3V  
    │
    └── KEY_DOWN ── GND

PA4 ── 10kΩ上拉 ── 3.3V
    │
    └── KEY_ENTER ── GND

PA5 ── 10kΩ上拉 ── 3.3V
    │
    └── KEY_BACK ── GND
```

## 🎮 **按键功能**

| 按键 | 功能 |
|------|------|
| **KEY_UP** | 向上选择/数值减少 |
| **KEY_DOWN** | 向下选择/数值增加 |
| **KEY_ENTER** | 确认选择/进入子菜单 |
| **KEY_BACK** | 返回上级菜单/取消操作 |

## 📱 **菜单结构**

### **主界面（天气显示）**：
- 显示当前城市天气信息
- 按 **ENTER** 进入主菜单

### **主菜单**：
```
=== 主菜单 ===
> 城市设置
  系统信息  
  返回天气
```

### **城市设置菜单**：
```
=== 城市设置 ===
当前: 汕头
> 快速选择
  自定义输入
  返回主菜单
```

### **快速选择城市**：
```
=== 选择城市 ===
> 北京 Beijing
  上海 Shanghai  
  深圳 Shenzhen
  广州 Guangzhou
  汕头 Shantou
  厦门 Xiamen
  杭州 Hangzhou
  成都 Chengdu
  重庆 Chongqing
  西安 Xian
  武汉 Wuhan
  南京 Nanjing
  苏州 Suzhou
  青岛 Qingdao
  大连 Dalian
```

### **系统信息**：
```
=== 系统信息 ===
WiFi: 已连接
IP: *************
运行: 1h
城市: Shantou
```

## ⚡ **操作流程**

### **切换城市**：
1. 在天气显示界面按 **ENTER**
2. 选择"城市设置" → 按 **ENTER**
3. 选择"快速选择" → 按 **ENTER**
4. 用 **UP/DOWN** 选择城市
5. 按 **ENTER** 确认选择
6. 系统自动返回天气显示，立即获取新城市天气

### **查看系统信息**：
1. 在天气显示界面按 **ENTER**
2. 选择"系统信息" → 按 **ENTER**
3. 查看WiFi状态、IP地址、运行时间等
4. 按 **BACK** 返回

## 🔄 **天气更新逻辑**

### **正常更新**：
- 每5分钟自动获取一次天气数据
- 保持原有的5分钟更新频率

### **城市切换后**：
- 立即获取新城市的天气数据
- 重置5分钟计时器
- 后续继续每5分钟更新一次

### **时序示例**：
```
时间轴：0分钟 → 3分钟 → 5分钟 → 10分钟 → 15分钟
事件：  启动    切换城市   自动更新  自动更新   自动更新
      ↓       ↓        ↓       ↓        ↓
     获取A城市 → 立即获取B城市 → 获取B城市 → 获取B城市 → 获取B城市
```

## 💾 **数据存储**

### **Flash存储**：
- 用户选择的城市自动保存到Flash
- 断电重启后保持上次选择的城市
- 存储位置：Flash最后2KB (0x0801F800)

### **默认设置**：
- 首次使用默认城市：汕头 (Shantou)
- Flash数据损坏时自动恢复默认设置

## 🛡️ **安全特性**

### **按键防抖**：
- 20ms硬件防抖，避免误触发
- 支持长按连续操作

### **菜单超时**：
- 30秒无操作自动返回天气显示
- 避免长时间停留在菜单界面

### **数据校验**：
- Flash数据带校验和验证
- 魔数验证确保数据完整性

## 🔍 **调试信息**

### **串口输出**：
所有操作都会通过UART2输出调试信息：
```
Key menu system initialized
Current city: Shantou
Key pressed: 2, Menu state: 1
Menu state changed to: 2
City changed to: Beijing
Weather update triggered for city: Beijing
```

### **状态监控**：
系统每5秒输出状态信息：
```
=== System Status ===
WiFi Status: Connected
IP Address: *************
Server Status: Running
System Uptime: 3600 seconds
Current City: Beijing
```

## ✅ **优势总结**

1. **无Web服务器冲突**：完全避免HTTP请求时的服务器中断
2. **直观操作**：物理按键，操作反馈明确
3. **数据持久化**：设置自动保存，断电不丢失
4. **扩展性强**：可轻松添加更多城市和功能
5. **可靠性高**：不受网络状态影响的本地操作

## 🚀 **下一步扩展**

1. **自定义城市输入**：实现字母输入功能
2. **更多设置选项**：温度单位、更新频率等
3. **天气预报**：显示未来几天天气
4. **闹钟功能**：基于天气的智能提醒

---

**注意**：确保按键硬件连接正确，上拉电阻必须安装，否则可能出现误触发或无响应。
