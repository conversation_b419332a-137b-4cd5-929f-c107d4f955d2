# 🌐 OpenWeatherMap真实API配置指南

## 🎯 **功能概述**

现在你的ESP-01S项目支持真实的天气数据获取！系统会自动：
- ✅ 检测API Key配置状态
- ✅ 调用真实OpenWeatherMap API
- ✅ 解析JSON天气数据
- ✅ 错误处理和自动重试
- ✅ 降级到模拟数据（API Key未配置时）

## 🔑 **配置API Key**

### 第1步：修改代码
在 `Core/Src/main.c` 文件中找到这一行：
```c
const char* OPENWEATHER_API_KEY = "YOUR_API_KEY_HERE";
```

将 `YOUR_API_KEY_HERE` 替换为你的真实API Key：
```c
const char* OPENWEATHER_API_KEY = "********************************";
```

### 第2步：编译下载
1. **编译项目** - Build → Rebuild All
2. **下载程序** - Flash → Download
3. **重启设备** - 按复位按钮

## 🚀 **API调用流程**

### 自动检测机制
```
启动 → 检查API Key → 选择数据源
  ↓
API Key = "YOUR_API_KEY_HERE" → 使用模拟数据
  ↓
API Key = 真实密钥 → 调用真实API
```

### API请求格式
```
GET /data/2.5/weather?q=Beijing&appid=你的API_KEY&units=metric&lang=zh_cn
Host: api.openweathermap.org
```

### 响应解析
系统会自动解析JSON响应中的：
- **温度** (`main.temp`) - 摄氏度
- **湿度** (`main.humidity`) - 百分比
- **天气描述** (`weather[0].description`) - 中文描述
- **城市名称** (`name`) - 城市名

## 🔄 **错误处理机制**

### 重试策略
1. **首次失败** → 5分钟后重试
2. **二次失败** → 5分钟后重试
3. **三次失败** → 等待下个正常更新周期（30秒）

### 错误类型处理
- **网络错误** → 自动重试
- **API错误** (404, 401等) → 显示错误信息
- **JSON解析失败** → 保持旧数据
- **数据验证失败** → 标记数据无效

### 状态显示
- **正常获取** → 显示实时天气数据
- **获取中** → "Getting Weather..." + 重试次数
- **WiFi断开** → "WiFi disconnected"
- **API失败** → "No data available"

## 📱 **测试步骤**

### 1. 模拟数据测试（API Key未配置）
- **预期显示**：`Partly Cloudy (Mock)`
- **温度**：25.5°C
- **湿度**：65%

### 2. 真实API测试（配置API Key后）
- **预期显示**：真实北京天气
- **温度**：实时温度
- **湿度**：实时湿度
- **描述**：中文天气描述

### 3. 错误测试
- **断开WiFi** → 显示"WiFi disconnected"
- **错误API Key** → 显示重试信息
- **网络超时** → 自动重试机制

## ⚙️ **自定义配置**

### 修改城市
在 `main.c` 中修改：
```c
#define WEATHER_CITY    "Shanghai"  // 改为你的城市
```

### 修改更新间隔
```c
#define WEATHER_UPDATE_INTERVAL  60000  // 改为1分钟
```

### 支持的城市格式
- **英文名**：`Beijing`, `Shanghai`, `Guangzhou`
- **中文名**：`北京`, `上海`, `广州`
- **带国家代码**：`Beijing,CN`, `London,UK`

## 🔍 **故障排除**

### 问题1：显示"No data available"
**可能原因**：
- API Key未配置或错误
- 网络连接问题
- API调用限制

**解决方案**：
1. 检查API Key是否正确
2. 确认WiFi连接正常
3. 等待重试机制生效

### 问题2：显示模拟数据
**原因**：API Key仍为默认值
**解决**：正确配置API Key并重新编译

### 问题3：重试次数过多
**原因**：API Key无效或网络问题
**解决**：
1. 验证API Key有效性
2. 检查网络连接
3. 确认API调用限额

## 📊 **API限制说明**

### 免费版限制
- **1000次/天** - 约每86秒一次
- **60次/分钟** - 足够使用
- **当前天气** - 支持
- **5天预报** - 支持

### 建议设置
- **更新间隔** ≥ 5分钟（避免超限）
- **重试间隔** = 5分钟（平衡及时性和限额）

## 🎉 **成功标志**

配置成功后应该看到：
- ✅ **真实温度数据** - 不再是25.5°C
- ✅ **真实湿度数据** - 不再是65%
- ✅ **中文天气描述** - 如"晴"、"多云"等
- ✅ **正确城市名** - 显示实际查询的城市
- ✅ **实时更新** - 数据会定期刷新

恭喜！你现在拥有了一个完整的物联网天气站！🌟
