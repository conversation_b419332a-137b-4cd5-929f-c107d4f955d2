Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.EXTI9_5_IRQHandler) for EXTI9_5_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    esp01s.o(i.ESP01S_CheckServerStatus) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_ConnectWiFi) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_ConnectWiFi) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_ConnectWiFi) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_ConnectWiFi) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_ConnectWiFi) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_ConnectWiFi) refers to strcpy.o(.text) for strcpy
    esp01s.o(i.ESP01S_ConnectWiFi) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_ConnectWiFi) refers to esp01s.o(i.ESP01S_GetLocalIP) for ESP01S_GetLocalIP
    esp01s.o(i.ESP01S_ConnectWiFi) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_DebugPrint) refers to vsnprintf.o(.text) for vsnprintf
    esp01s.o(i.ESP01S_DebugPrint) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_DebugPrint) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_DebugPrint) refers to usart.o(.bss) for huart2
    esp01s.o(i.ESP01S_DebugPrintATCommand) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to _printf_pad.o(.text) for _printf_pre_padding
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_DebugPrintBuffer) refers to usart.o(.bss) for huart2
    esp01s.o(i.ESP01S_DebugPrintResponse) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_DisconnectWiFi) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_DisconnectWiFi) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp01s.o(i.ESP01S_DisconnectWiFi) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_GetLocalIP) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_GetLocalIP) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_GetLocalIP) refers to strchr.o(.text) for strchr
    esp01s.o(i.ESP01S_GetLocalIP) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_GetLocalIP) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_GetNetworkInfo) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_GetWeather) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_GetWeatherData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_GetWeatherData) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_GetWeatherData) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_GetWeatherData) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_GetWeatherData) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_GetWeatherData) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_GetWeatherData) refers to esp01s.o(i.ESP01S_HTTPGetSimplified) for ESP01S_HTTPGetSimplified
    esp01s.o(i.ESP01S_GetWeatherData) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_GetWeatherData) refers to esp01s.o(i.ESP01S_DebugPrintBuffer) for ESP01S_DebugPrintBuffer
    esp01s.o(i.ESP01S_GetWeatherData) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_GetWeatherData) refers to esp01s.o(i.ESP01S_ParseWeatherJSON) for ESP01S_ParseWeatherJSON
    esp01s.o(i.ESP01S_GetWeatherData) refers to strcpy.o(.text) for strcpy
    esp01s.o(i.ESP01S_GetWeatherData) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_GetWeatherData) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_GetWeatherData) refers to main.o(.data) for OPENWEATHER_API_KEY
    esp01s.o(i.ESP01S_GetWeatherData) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_GetWeatherRequestState) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_GetWiFiStatus) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_HTTPGet) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_HTTPGet) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_HTTPGet) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_HTTPGet) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_HTTPGet) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_HTTPGet) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp01s.o(i.ESP01S_HTTPGet) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_HTTPGet) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_HTTPGet) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_HTTPGet) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_HTTPGet) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_HTTPGet) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_HTTPGet) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_HTTPGet) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_HTTPGet) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_HTTPGet) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    esp01s.o(i.ESP01S_HTTPGet) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_HTTPGet) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_HTTPGetEnhanced) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_HTTPGetSimple) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) for HAL_UART_AbortReceive_IT
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_HTTPGetSimplified) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to esp01s.o(i.ESP01S_SendData) for ESP01S_SendData
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to __2sprintf.o(.text) for __2sprintf
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_HandleHTTPRequest) refers to esp01s.o(.conststring) for .conststring
    esp01s.o(i.ESP01S_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    esp01s.o(i.ESP01S_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    esp01s.o(i.ESP01S_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_Init) refers to esp01s.o(i.ESP01S_Reset) for ESP01S_Reset
    esp01s.o(i.ESP01S_Init) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_Init) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_Init) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_InitDebug) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_InitDebug) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    esp01s.o(i.ESP01S_InitDebug) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_InitDebug) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    esp01s.o(i.ESP01S_InitDebug) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_InitDebug) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_InitDebug) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_IsWeatherRequestInProgress) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_ParseJSONString) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_ParseJSONString) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp01s.o(i.ESP01S_ParseJSONString) refers to _printf_str.o(.text) for _printf_str
    esp01s.o(i.ESP01S_ParseJSONString) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_ParseJSONString) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_ParseJSONString) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_ParseJSONString) refers to strchr.o(.text) for strchr
    esp01s.o(i.ESP01S_ParseJSONString) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to strchr.o(.text) for strchr
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to atof.o(i.atof) for atof
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to atoi.o(.text) for atoi
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to strcpy.o(.text) for strcpy
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to esp01s.o(i.ESP01S_ParseJSONString) for ESP01S_ParseJSONString
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_ParseWeatherJSON) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_ProcessResponse) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_ProcessResponse) refers to esp01s.o(i.ESP01S_HandleHTTPRequest) for ESP01S_HandleHTTPRequest
    esp01s.o(i.ESP01S_ProcessResponse) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_ProcessWeatherRequest) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_ProcessWeatherRequest) refers to esp01s.o(i.ESP01S_GetWeatherData) for ESP01S_GetWeatherData
    esp01s.o(i.ESP01S_ProcessWeatherRequest) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_ProcessWeatherRequest) refers to strncpy.o(.text) for strncpy
    esp01s.o(i.ESP01S_ProcessWeatherRequest) refers to strcpy.o(.text) for strcpy
    esp01s.o(i.ESP01S_ProcessWeatherRequest) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_Reset) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    esp01s.o(i.ESP01S_Reset) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_Reset) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_Reset) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_SendCommand) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_SendCommand) refers to esp01s.o(i.ESP01S_DebugPrintATCommand) for ESP01S_DebugPrintATCommand
    esp01s.o(i.ESP01S_SendCommand) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_SendCommand) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_SendCommand) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_SendCommand) refers to esp01s.o(i.ESP01S_DebugPrintResponse) for ESP01S_DebugPrintResponse
    esp01s.o(i.ESP01S_SendCommand) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_SendCommand) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    esp01s.o(i.ESP01S_SendCommand) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_SendCommand) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_SendCommand) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_SendData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_SendData) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_SendData) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_SendData) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_SendData) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_SendData) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_SendData) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_SendData) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_SendData) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_SendData) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_SendData) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_SendData) refers to usart.o(.bss) for huart1
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to esp01s.o(i.ESP01S_SendData) for ESP01S_SendData
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to __2sprintf.o(.text) for __2sprintf
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to esp01s.o(.conststring) for .conststring
    esp01s.o(i.ESP01S_SendSimpleResponse) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_StartServer) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_StartServer) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_StartServer) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_StartServer) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_StartServer) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_StartServer) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_StartWeatherRequest) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_StartWeatherRequest) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_StartWeatherRequest) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_TestATCommands) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_TestATCommands) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_TestATCommands) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_TestATCommands) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to _printf_dec.o(.text) for _printf_int_dec
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to strlen.o(.text) for strlen
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to __2snprintf.o(.text) for __2snprintf
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to strstr.o(.text) for strstr
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to esp01s.o(.bss) for .bss
    esp01s.o(i.ESP01S_TestHTTPConnection) refers to usart.o(.bss) for huart1
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_PowerOnInit) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Init) for OLED_Init
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F6x8
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowWeather) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled.o(i.OLED_ShowWeather) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    oled.o(i.OLED_ShowWeather) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    oled.o(i.OLED_ShowWeather) refers to _printf_str.o(.text) for _printf_str
    oled.o(i.OLED_ShowWeather) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled.o(i.OLED_ShowWeather) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled.o(i.OLED_ShowWeather) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    oled.o(i.OLED_ShowWeather) refers to _printf_dec.o(.text) for _printf_int_dec
    oled.o(i.OLED_ShowWeather) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled.o(i.OLED_ShowWeather) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_ShowWeather) refers to __2sprintf.o(.text) for __2sprintf
    oled.o(i.OLED_ShowWeather) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_ShowWeather) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled.o(i.OLED_ShowWeather) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled.o(i.OLED_ShowWeather) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_ShowWeather) refers to main.o(.data) for wifi_connected
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_CheckBufferIntegrity) for OLED_CheckBufferIntegrity
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteCommand) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteCommand) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_WriteCommand) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_WriteData) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    oled.o(i.OLED_WriteData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteData) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_WriteData) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteData) refers to i2c.o(.bss) for hi2c1
    main.o(i.CheckWiFiConnection) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.CheckWiFiConnection) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    main.o(i.CheckWiFiConnection) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.CheckWiFiConnection) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.CheckWiFiConnection) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.CheckWiFiConnection) refers to esp01s.o(i.ESP01S_ConnectWiFi) for ESP01S_ConnectWiFi
    main.o(i.CheckWiFiConnection) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.CheckWiFiConnection) refers to key_menu.o(i.City_GetCurrent) for City_GetCurrent
    main.o(i.CheckWiFiConnection) refers to esp01s.o(i.ESP01S_GetWeatherData) for ESP01S_GetWeatherData
    main.o(i.CheckWiFiConnection) refers to main.o(.data) for .data
    main.o(i.ESP_Test_WebServer) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.ESP_Test_WebServer) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.ESP_Test_WebServer) refers to esp01s.o(i.ESP01S_StartServer) for ESP01S_StartServer
    main.o(i.ESP_Test_WebServer) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.ESP_Test_WebServer) refers to main.o(.data) for .data
    main.o(i.ESP_Test_WiFiConnect) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.ESP_Test_WiFiConnect) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.ESP_Test_WiFiConnect) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.ESP_Test_WiFiConnect) refers to esp01s.o(i.ESP01S_ConnectWiFi) for ESP01S_ConnectWiFi
    main.o(i.ESP_Test_WiFiConnect) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    main.o(i.ESP_Test_WiFiConnect) refers to key_menu.o(i.City_GetCurrent) for City_GetCurrent
    main.o(i.ESP_Test_WiFiConnect) refers to esp01s.o(i.ESP01S_GetWeatherData) for ESP01S_GetWeatherData
    main.o(i.ESP_Test_WiFiConnect) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.ESP_Test_WiFiConnect) refers to main.o(.data) for .data
    main.o(i.ESP_Test_WiFiConnect) refers to esp01s.o(.bss) for esp01s
    main.o(i.ESP_Test_WiFiScan) refers to esp01s.o(i.ESP01S_SendCommand) for ESP01S_SendCommand
    main.o(i.OLED_DisplayStatus) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.OLED_DisplayStatus) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.OLED_DisplayStatus) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.OLED_DisplayStatus) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.OLED_DisplayStatus) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.OLED_DisplayStatus) refers to esp01s.o(i.ESP01S_GetWiFiStatus) for ESP01S_GetWiFiStatus
    main.o(i.OLED_DisplayStatus) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.OLED_DisplayStatus) refers to __2snprintf.o(.text) for __2snprintf
    main.o(i.OLED_DisplayStatus) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.OLED_DisplayStatus) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.OLED_DisplayStatus) refers to main.o(.data) for .data
    main.o(i.OLED_DisplayStatus) refers to esp01s.o(.bss) for esp01s
    main.o(i.OLED_DisplayStatus) refers to main.o(.bss) for .bss
    main.o(i.OLED_DisplayWiFiInfo) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.OLED_DisplayWiFiInfo) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.OLED_DisplayWiFiInfo) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.OLED_DisplayWiFiInfo) refers to main.o(.data) for .data
    main.o(i.OLED_DisplayWiFiInfo) refers to esp01s.o(.bss) for esp01s
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.UpdateWeatherData) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    main.o(i.UpdateWeatherData) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.UpdateWeatherData) refers to strcmpv7m.o(.text) for strcmp
    main.o(i.UpdateWeatherData) refers to key_menu.o(i.City_GetCurrent) for City_GetCurrent
    main.o(i.UpdateWeatherData) refers to esp01s.o(i.ESP01S_GetWeatherData) for ESP01S_GetWeatherData
    main.o(i.UpdateWeatherData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState) for HAL_I2C_GetState
    main.o(i.UpdateWeatherData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) for HAL_I2C_DeInit
    main.o(i.UpdateWeatherData) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.UpdateWeatherData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    main.o(i.UpdateWeatherData) refers to main.o(.data) for .data
    main.o(i.UpdateWeatherData) refers to main.o(.conststring) for .conststring
    main.o(i.UpdateWeatherData) refers to i2c.o(.bss) for hi2c1
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    main.o(i.main) refers to oled.o(i.OLED_PowerOnInit) for OLED_PowerOnInit
    main.o(i.main) refers to key_menu.o(i.KeyMenu_Init) for KeyMenu_Init
    main.o(i.main) refers to esp01s.o(i.ESP01S_InitDebug) for ESP01S_InitDebug
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Update) for OLED_Update
    main.o(i.main) refers to main.o(i.ESP_Test_WiFiConnect) for ESP_Test_WiFiConnect
    main.o(i.main) refers to main.o(i.ESP_Test_WebServer) for ESP_Test_WebServer
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to esp01s.o(i.ESP01S_ProcessResponse) for ESP01S_ProcessResponse
    main.o(i.main) refers to esp01s.o(i.ESP01S_ProcessWeatherRequest) for ESP01S_ProcessWeatherRequest
    main.o(i.main) refers to main.o(i.UpdateWeatherData) for UpdateWeatherData
    main.o(i.main) refers to key_menu.o(i.KeyMenu_Process) for KeyMenu_Process
    main.o(i.main) refers to key_menu.o(i.Menu_Display) for Menu_Display
    main.o(i.main) refers to key_menu.o(i.City_GetCurrent) for City_GetCurrent
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to key_menu.o(.data) for g_current_menu_state
    main.o(i.main) refers to esp01s.o(.bss) for esp01s
    main.o(.data) refers to main.o(.conststring) for .conststring
    main.o(.data) refers to main.o(.conststring) for .conststring
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.HAL_UART_RxCpltCallback) refers to esp01s.o(.bss) for esp01s
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.EXTI0_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.EXTI1_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.EXTI4_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.EXTI9_5_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to key_menu.o(i.TIM1_DebounceCallback) for TIM1_DebounceCallback
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to tim.o(.bss) for htim1
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to key_menu.o(i.TIM3_TimeoutCallback) for TIM3_TimeoutCallback
    stm32f1xx_it.o(i.TIM3_IRQHandler) refers to tim.o(.bss) for htim3
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    key_menu.o(i.Check_I2C_Status) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState) for HAL_I2C_GetState
    key_menu.o(i.Check_I2C_Status) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Check_I2C_Status) refers to key_menu.o(i.Force_I2C_Reset) for Force_I2C_Reset
    key_menu.o(i.Check_I2C_Status) refers to i2c.o(.bss) for hi2c1
    key_menu.o(i.City_GetCurrent) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.City_SetCurrent) refers to strncpy.o(.text) for strncpy
    key_menu.o(i.City_SetCurrent) refers to key_menu.o(i.Flash_SaveUserSettings) for Flash_SaveUserSettings
    key_menu.o(i.City_SetCurrent) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.City_SetCurrent) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.City_TriggerWeatherUpdate) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.City_TriggerWeatherUpdate) refers to main.o(.data) for weather_update_requested
    key_menu.o(i.City_TriggerWeatherUpdate) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.Flash_LoadUserSettings) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Flash_LoadUserSettings) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    key_menu.o(i.Flash_LoadUserSettings) refers to key_menu.o(i.Flash_CalculateChecksum) for Flash_CalculateChecksum
    key_menu.o(i.Flash_LoadUserSettings) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.Flash_SaveUserSettings) refers to key_menu.o(i.Flash_CalculateChecksum) for Flash_CalculateChecksum
    key_menu.o(i.Flash_SaveUserSettings) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    key_menu.o(i.Flash_SaveUserSettings) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    key_menu.o(i.Flash_SaveUserSettings) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    key_menu.o(i.Flash_SaveUserSettings) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    key_menu.o(i.Flash_SaveUserSettings) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Flash_SaveUserSettings) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.Force_I2C_Reset) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Force_I2C_Reset) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) for HAL_I2C_DeInit
    key_menu.o(i.Force_I2C_Reset) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    key_menu.o(i.Force_I2C_Reset) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    key_menu.o(i.Force_I2C_Reset) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    key_menu.o(i.Force_I2C_Reset) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    key_menu.o(i.Force_I2C_Reset) refers to i2c.o(.bss) for hi2c1
    key_menu.o(i.HAL_GPIO_EXTI_Callback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_menu.o(i.HAL_GPIO_EXTI_Callback) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.HAL_GPIO_EXTI_Callback) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_menu.o(i.HAL_GPIO_EXTI_Callback) refers to key_menu.o(i.KeyMenu_HandleKeyPress) for KeyMenu_HandleKeyPress
    key_menu.o(i.HAL_GPIO_EXTI_Callback) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.KeyMenu_GetKeyState) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to key_menu.o(i.Menu_HandleInput) for Menu_HandleInput
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to key_menu.o(i.Menu_Display) for Menu_Display
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to key_menu.o(.data) for .data
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.KeyMenu_HandleKeyPress) refers to main.o(.data) for display_update_requested
    key_menu.o(i.KeyMenu_Init) refers to key_menu.o(i.Flash_LoadUserSettings) for Flash_LoadUserSettings
    key_menu.o(i.KeyMenu_Init) refers to strcpy.o(.text) for strcpy
    key_menu.o(i.KeyMenu_Init) refers to key_menu.o(i.Flash_SaveUserSettings) for Flash_SaveUserSettings
    key_menu.o(i.KeyMenu_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    key_menu.o(i.KeyMenu_Init) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.KeyMenu_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_menu.o(i.KeyMenu_Init) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.KeyMenu_Init) refers to tim.o(.bss) for htim3
    key_menu.o(i.KeyMenu_Init) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.KeyMenu_Process) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_menu.o(i.KeyMenu_Process) refers to key_menu.o(i.Menu_Display) for Menu_Display
    key_menu.o(i.KeyMenu_Process) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_ChangeState) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    key_menu.o(i.Menu_ChangeState) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_ChangeState) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.Menu_ChangeState) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_Display) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    key_menu.o(i.Menu_Display) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ShowWeatherDisplay) for Menu_ShowWeatherDisplay
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ShowMainMenu) for Menu_ShowMainMenu
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ShowCitySetting) for Menu_ShowCitySetting
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ShowCityQuick) for Menu_ShowCityQuick
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ShowCityInput) for Menu_ShowCityInput
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ShowSystemInfo) for Menu_ShowSystemInfo
    key_menu.o(i.Menu_Display) refers to key_menu.o(i.Menu_ChangeState) for Menu_ChangeState
    key_menu.o(i.Menu_Display) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_Display) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.Menu_ForceUpdate) refers to key_menu.o(i.Menu_Display) for Menu_Display
    key_menu.o(i.Menu_ForceUpdate) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_HandleInput) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_HandleInput) refers to key_menu.o(i.Menu_ChangeState) for Menu_ChangeState
    key_menu.o(i.Menu_HandleInput) refers to key_menu.o(i.City_SetCurrent) for City_SetCurrent
    key_menu.o(i.Menu_HandleInput) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.Menu_HandleInput) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_HandleInput) refers to main.o(.data) for weather_update_requested
    key_menu.o(i.Menu_ResetTimeout) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_ShowCityInput) refers to oled.o(i.OLED_Clear) for OLED_Clear
    key_menu.o(i.Menu_ShowCityInput) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_menu.o(i.Menu_ShowCityInput) refers to oled.o(i.OLED_Update) for OLED_Update
    key_menu.o(i.Menu_ShowCityQuick) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_menu.o(i.Menu_ShowCityQuick) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_menu.o(i.Menu_ShowCityQuick) refers to _printf_str.o(.text) for _printf_str
    key_menu.o(i.Menu_ShowCityQuick) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_menu.o(i.Menu_ShowCityQuick) refers to _printf_dec.o(.text) for _printf_int_dec
    key_menu.o(i.Menu_ShowCityQuick) refers to oled.o(i.OLED_Clear) for OLED_Clear
    key_menu.o(i.Menu_ShowCityQuick) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_menu.o(i.Menu_ShowCityQuick) refers to __2snprintf.o(.text) for __2snprintf
    key_menu.o(i.Menu_ShowCityQuick) refers to oled.o(i.OLED_Update) for OLED_Update
    key_menu.o(i.Menu_ShowCityQuick) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_ShowCityQuick) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.Menu_ShowCitySetting) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_menu.o(i.Menu_ShowCitySetting) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_menu.o(i.Menu_ShowCitySetting) refers to _printf_str.o(.text) for _printf_str
    key_menu.o(i.Menu_ShowCitySetting) refers to oled.o(i.OLED_Clear) for OLED_Clear
    key_menu.o(i.Menu_ShowCitySetting) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_menu.o(i.Menu_ShowCitySetting) refers to __2snprintf.o(.text) for __2snprintf
    key_menu.o(i.Menu_ShowCitySetting) refers to oled.o(i.OLED_Update) for OLED_Update
    key_menu.o(i.Menu_ShowCitySetting) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_ShowCitySetting) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.Menu_ShowCitySetting) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.Menu_ShowCitySetting) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_ShowMainMenu) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_menu.o(i.Menu_ShowMainMenu) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_menu.o(i.Menu_ShowMainMenu) refers to _printf_str.o(.text) for _printf_str
    key_menu.o(i.Menu_ShowMainMenu) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_ShowMainMenu) refers to oled.o(i.OLED_Clear) for OLED_Clear
    key_menu.o(i.Menu_ShowMainMenu) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_menu.o(i.Menu_ShowMainMenu) refers to __2snprintf.o(.text) for __2snprintf
    key_menu.o(i.Menu_ShowMainMenu) refers to key_menu.o(i.Safe_OLED_Update) for Safe_OLED_Update
    key_menu.o(i.Menu_ShowMainMenu) refers to key_menu.o(.data) for .data
    key_menu.o(i.Menu_ShowMainMenu) refers to key_menu.o(.constdata) for .constdata
    key_menu.o(i.Menu_ShowSystemInfo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_menu.o(i.Menu_ShowSystemInfo) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    key_menu.o(i.Menu_ShowSystemInfo) refers to _printf_str.o(.text) for _printf_str
    key_menu.o(i.Menu_ShowSystemInfo) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    key_menu.o(i.Menu_ShowSystemInfo) refers to _printf_dec.o(.text) for _printf_int_dec
    key_menu.o(i.Menu_ShowSystemInfo) refers to oled.o(i.OLED_Clear) for OLED_Clear
    key_menu.o(i.Menu_ShowSystemInfo) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_menu.o(i.Menu_ShowSystemInfo) refers to __2snprintf.o(.text) for __2snprintf
    key_menu.o(i.Menu_ShowSystemInfo) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_menu.o(i.Menu_ShowSystemInfo) refers to oled.o(i.OLED_Update) for OLED_Update
    key_menu.o(i.Menu_ShowSystemInfo) refers to main.o(.data) for wifi_connected
    key_menu.o(i.Menu_ShowSystemInfo) refers to esp01s.o(.bss) for esp01s
    key_menu.o(i.Menu_ShowSystemInfo) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to _printf_dec.o(.text) for _printf_int_dec
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to esp01s.o(i.ESP01S_GetWeather) for ESP01S_GetWeather
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to oled.o(i.OLED_ShowWeather) for OLED_ShowWeather
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to __2snprintf.o(.text) for __2snprintf
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to oled.o(i.OLED_Update) for OLED_Update
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.Menu_ShowWeatherDisplay) refers to key_menu.o(.data) for .data
    key_menu.o(i.Safe_OLED_Update) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_menu.o(i.Safe_OLED_Update) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState) for HAL_I2C_GetState
    key_menu.o(i.Safe_OLED_Update) refers to key_menu.o(i.Check_I2C_Status) for Check_I2C_Status
    key_menu.o(i.Safe_OLED_Update) refers to oled.o(i.OLED_Update) for OLED_Update
    key_menu.o(i.Safe_OLED_Update) refers to key_menu.o(.data) for .data
    key_menu.o(i.Safe_OLED_Update) refers to i2c.o(.bss) for hi2c1
    key_menu.o(i.TIM1_DebounceCallback) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT) for HAL_TIM_Base_Stop_IT
    key_menu.o(i.TIM1_DebounceCallback) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.TIM1_DebounceCallback) refers to tim.o(.bss) for htim1
    key_menu.o(i.TIM1_DebounceCallback) refers to key_menu.o(.data) for .data
    key_menu.o(i.TIM1_DebounceCallback) refers to key_menu.o(.bss) for .bss
    key_menu.o(i.TIM3_TimeoutCallback) refers to key_menu.o(i.Menu_ChangeState) for Menu_ChangeState
    key_menu.o(i.TIM3_TimeoutCallback) refers to esp01s.o(i.ESP01S_DebugPrint) for ESP01S_DebugPrint
    key_menu.o(i.TIM3_TimeoutCallback) refers to key_menu.o(.data) for .data
    key_menu.o(.constdata) refers to key_menu.o(.conststring) for .conststring
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to key_menu.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.__ldexp$lsc) for __ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.__ldexp$lsc) for __ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.__ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.__ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.__ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing esp01s.o(.rev16_text), (4 bytes).
    Removing esp01s.o(.revsh_text), (4 bytes).
    Removing esp01s.o(.rrx_text), (6 bytes).
    Removing esp01s.o(i.ESP01S_CheckServerStatus), (32 bytes).
    Removing esp01s.o(i.ESP01S_DisconnectWiFi), (76 bytes).
    Removing esp01s.o(i.ESP01S_GetNetworkInfo), (84 bytes).
    Removing esp01s.o(i.ESP01S_GetWeatherRequestState), (12 bytes).
    Removing esp01s.o(i.ESP01S_GetWiFiStatus), (12 bytes).
    Removing esp01s.o(i.ESP01S_HTTPGet), (596 bytes).
    Removing esp01s.o(i.ESP01S_HTTPGetEnhanced), (1204 bytes).
    Removing esp01s.o(i.ESP01S_HTTPGetSimple), (556 bytes).
    Removing esp01s.o(i.ESP01S_Init), (180 bytes).
    Removing esp01s.o(i.ESP01S_IsWeatherRequestInProgress), (28 bytes).
    Removing esp01s.o(i.ESP01S_Reset), (72 bytes).
    Removing esp01s.o(i.ESP01S_SendSimpleResponse), (160 bytes).
    Removing esp01s.o(i.ESP01S_StartWeatherRequest), (96 bytes).
    Removing esp01s.o(i.ESP01S_TestATCommands), (300 bytes).
    Removing esp01s.o(i.ESP01S_TestHTTPConnection), (564 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DrawArc), (586 bytes).
    Removing oled.o(i.OLED_DrawCircle), (318 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (692 bytes).
    Removing oled.o(i.OLED_DrawLine), (234 bytes).
    Removing oled.o(i.OLED_DrawPoint), (52 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (116 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (196 bytes).
    Removing oled.o(i.OLED_GetPoint), (56 bytes).
    Removing oled.o(i.OLED_I2C_SendByte), (2 bytes).
    Removing oled.o(i.OLED_I2C_Start), (2 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (2 bytes).
    Removing oled.o(i.OLED_IsInAngle), (96 bytes).
    Removing oled.o(i.OLED_Pow), (14 bytes).
    Removing oled.o(i.OLED_Printf), (42 bytes).
    Removing oled.o(i.OLED_Reverse), (40 bytes).
    Removing oled.o(i.OLED_ReverseArea), (88 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (64 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (190 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (72 bytes).
    Removing oled.o(i.OLED_ShowNum), (70 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (94 bytes).
    Removing oled.o(i.OLED_UpdateArea), (96 bytes).
    Removing oled.o(i.OLED_W_SCL), (2 bytes).
    Removing oled.o(i.OLED_W_SDA), (2 bytes).
    Removing oled.o(i.OLED_pnpoly), (116 bytes).
    Removing oled_data.o(.constdata), (32 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.CheckWiFiConnection), (176 bytes).
    Removing main.o(i.ESP_PrintStatus), (2 bytes).
    Removing main.o(i.ESP_Test_WiFiScan), (28 bytes).
    Removing main.o(i.OLED_DisplayStatus), (320 bytes).
    Removing main.o(i.OLED_DisplayWiFiInfo), (164 bytes).
    Removing main.o(.bss), (32 bytes).
    Removing main.o(.conststring), (23 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (76 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (84 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing key_menu.o(.rev16_text), (4 bytes).
    Removing key_menu.o(.revsh_text), (4 bytes).
    Removing key_menu.o(.rrx_text), (6 bytes).
    Removing key_menu.o(i.City_TriggerWeatherUpdate), (204 bytes).
    Removing key_menu.o(i.KeyMenu_GetKeyState), (28 bytes).
    Removing key_menu.o(i.Menu_ForceUpdate), (40 bytes).
    Removing key_menu.o(i.Menu_ResetTimeout), (12 bytes).
    Removing key_menu.o(.constdata), (1 bytes).
    Removing key_menu.o(.constdata), (1 bytes).
    Removing key_menu.o(.constdata), (1 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (592 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (604 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (400 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (54 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (416 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig), (68 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA), (144 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).

499 unused section(s) (total 52071 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ..\Core\Src\OLED.c                       0x00000000   Number         0  oled.o ABSOLUTE
    ..\Core\Src\OLED_Data.c                  0x00000000   Number         0  oled_data.o ABSOLUTE
    ..\Core\Src\esp01s.c                     0x00000000   Number         0  esp01s.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\key_menu.c                   0x00000000   Number         0  key_menu.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\\Core\\Src\\OLED.c                    0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Core\\Src\\esp01s.c                  0x00000000   Number         0  esp01s.o ABSOLUTE
    ..\\Core\\Src\\key_menu.c                0x00000000   Number         0  key_menu.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000160   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000166   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800016c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000172   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000178   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800017e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000184   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800018e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000194   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800019a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001a0   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001a6   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080001ac   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080001b2   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080001b8   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080001be   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080001c4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080001ca   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080001d4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080001da   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080001e0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080001e6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080001ec   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001f0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001f2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001f8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080001f8   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000204   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800020e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000210   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x08000212   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x08000214   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000214   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000214   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800021a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800021a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800021e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800021e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000226   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000228   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000228   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800022c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000234   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x08000274   Section       38  llushr.o(.text)
    .text                                    0x0800029c   Section        0  vsnprintf.o(.text)
    .text                                    0x080002d0   Section        0  __2sprintf.o(.text)
    .text                                    0x080002fc   Section        0  __2snprintf.o(.text)
    .text                                    0x08000334   Section        0  _printf_pad.o(.text)
    .text                                    0x08000382   Section        0  _printf_str.o(.text)
    .text                                    0x080003d4   Section        0  _printf_dec.o(.text)
    .text                                    0x0800044c   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x0800044d   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x080004e0   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000668   Section        0  atoi.o(.text)
    .text                                    0x08000682   Section        0  strchr.o(.text)
    .text                                    0x08000696   Section        0  strstr.o(.text)
    .text                                    0x080006ba   Section        0  strcpy.o(.text)
    .text                                    0x08000702   Section        0  strlen.o(.text)
    .text                                    0x08000740   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080007ca   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x0800082e   Section       68  rt_memclr.o(.text)
    .text                                    0x08000872   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080008c0   Section       86  strncpy.o(.text)
    .text                                    0x08000918   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000998   Section        0  heapauxi.o(.text)
    .text                                    0x080009a0   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080009a8   Section        0  _rserrno.o(.text)
    .text                                    0x080009be   Section        0  _printf_truncate.o(.text)
    .text                                    0x080009e2   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000a94   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000abc   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000abf   Thumb Code   428  _printf_fp_dec.o(.text)
    .text                                    0x08000ed8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000ed9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000f08   Section        0  _sputc.o(.text)
    .text                                    0x08000f12   Section        0  _snputc.o(.text)
    .text                                    0x08000f22   Section        0  _printf_char.o(.text)
    .text                                    0x08000f50   Section        0  _printf_wctomb.o(.text)
    .text                                    0x0800100c   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08001088   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08001089   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x080010f8   Section        0  strtod.o(.text)
    _local_sscanf                            0x080010f9   Thumb Code    60  strtod.o(.text)
    .text                                    0x08001198   Section        0  strtol.o(.text)
    .text                                    0x08001208   Section        8  libspace.o(.text)
    .text                                    0x08001210   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001220   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001228   Section      138  lludiv10.o(.text)
    .text                                    0x080012b2   Section        0  isspace.o(.text)
    .text                                    0x080012c4   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080015c0   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001640   Section        0  _printf_wchar.o(.text)
    .text                                    0x0800166c   Section        0  _sgetc.o(.text)
    .text                                    0x080016ac   Section        0  _strtoul.o(.text)
    .text                                    0x0800174c   Section        0  bigflt0.o(.text)
    .text                                    0x08001828   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001868   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080018b2   Section        0  _chval.o(.text)
    .text                                    0x080018d0   Section        0  scanf_fp.o(.text)
    _fp_value                                0x080018d1   Thumb Code   508  scanf_fp.o(.text)
    .text                                    0x08001d74   Section        0  exit.o(.text)
    .text                                    0x08001d80   Section        0  scanf_hexfp.o(.text)
    .text                                    0x08002098   Section        0  scanf_infnan.o(.text)
    .text                                    0x080021cc   Section        0  sys_exit.o(.text)
    .text                                    0x080021d8   Section       38  llshl.o(.text)
    .text                                    0x080021fe   Section        2  use_no_semi.o(.text)
    .text                                    0x08002200   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08002200   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x0800223e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002284   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080022e4   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x0800261c   Section       84  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08002670   Section      198  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002736   Section       40  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x0800275e   Section       40  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x08002786   Section       40  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x080027ae   Section       40  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x080027d6   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08002a1a   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.Check_I2C_Status                       0x08002a1c   Section        0  key_menu.o(i.Check_I2C_Status)
    i.City_GetCurrent                        0x08002af0   Section        0  key_menu.o(i.City_GetCurrent)
    i.City_SetCurrent                        0x08002af8   Section        0  key_menu.o(i.City_SetCurrent)
    i.DebugMon_Handler                       0x08002b40   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.ESP01S_ConnectWiFi                     0x08002b44   Section        0  esp01s.o(i.ESP01S_ConnectWiFi)
    i.ESP01S_DebugPrint                      0x08002bc8   Section        0  esp01s.o(i.ESP01S_DebugPrint)
    i.ESP01S_DebugPrintATCommand             0x08002c0c   Section        0  esp01s.o(i.ESP01S_DebugPrintATCommand)
    i.ESP01S_DebugPrintBuffer                0x08002c24   Section        0  esp01s.o(i.ESP01S_DebugPrintBuffer)
    i.ESP01S_DebugPrintResponse              0x08002cc4   Section        0  esp01s.o(i.ESP01S_DebugPrintResponse)
    i.ESP01S_GetLocalIP                      0x08002cdc   Section        0  esp01s.o(i.ESP01S_GetLocalIP)
    i.ESP01S_GetWeather                      0x08002d3c   Section        0  esp01s.o(i.ESP01S_GetWeather)
    i.ESP01S_GetWeatherData                  0x08002d44   Section        0  esp01s.o(i.ESP01S_GetWeatherData)
    i.ESP01S_HTTPGetSimplified               0x08003258   Section        0  esp01s.o(i.ESP01S_HTTPGetSimplified)
    i.ESP01S_HandleHTTPRequest               0x08003b8c   Section        0  esp01s.o(i.ESP01S_HandleHTTPRequest)
    i.ESP01S_InitDebug                       0x08003c0c   Section        0  esp01s.o(i.ESP01S_InitDebug)
    i.ESP01S_ParseJSONString                 0x08003d2c   Section        0  esp01s.o(i.ESP01S_ParseJSONString)
    i.ESP01S_ParseWeatherJSON                0x08003d88   Section        0  esp01s.o(i.ESP01S_ParseWeatherJSON)
    i.ESP01S_ProcessResponse                 0x08004400   Section        0  esp01s.o(i.ESP01S_ProcessResponse)
    i.ESP01S_ProcessWeatherRequest           0x08004490   Section        0  esp01s.o(i.ESP01S_ProcessWeatherRequest)
    i.ESP01S_SendCommand                     0x08004594   Section        0  esp01s.o(i.ESP01S_SendCommand)
    i.ESP01S_SendData                        0x08004728   Section        0  esp01s.o(i.ESP01S_SendData)
    i.ESP01S_StartServer                     0x080047d4   Section        0  esp01s.o(i.ESP01S_StartServer)
    i.ESP_Test_WebServer                     0x08004858   Section        0  main.o(i.ESP_Test_WebServer)
    i.ESP_Test_WiFiConnect                   0x080048d4   Section        0  main.o(i.ESP_Test_WiFiConnect)
    i.EXTI0_IRQHandler                       0x08004a1c   Section        0  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI1_IRQHandler                       0x08004a22   Section        0  stm32f1xx_it.o(i.EXTI1_IRQHandler)
    i.EXTI4_IRQHandler                       0x08004a28   Section        0  stm32f1xx_it.o(i.EXTI4_IRQHandler)
    i.EXTI9_5_IRQHandler                     0x08004a2e   Section        0  stm32f1xx_it.o(i.EXTI9_5_IRQHandler)
    i.Error_Handler                          0x08004a34   Section        0  main.o(i.Error_Handler)
    i.FLASH_MassErase                        0x08004a38   Section        0  stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase)
    FLASH_MassErase                          0x08004a39   Thumb Code    26  stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase)
    i.FLASH_PageErase                        0x08004a5c   Section        0  stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase)
    i.FLASH_Program_HalfWord                 0x08004a80   Section        0  stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord)
    FLASH_Program_HalfWord                   0x08004a81   Thumb Code    20  stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord)
    i.FLASH_SetErrorCode                     0x08004a9c   Section        0  stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode)
    FLASH_SetErrorCode                       0x08004a9d   Thumb Code    84  stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode)
    i.FLASH_WaitForLastOperation             0x08004af8   Section        0  stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    i.Flash_CalculateChecksum                0x08004b4c   Section        0  key_menu.o(i.Flash_CalculateChecksum)
    i.Flash_LoadUserSettings                 0x08004b60   Section        0  key_menu.o(i.Flash_LoadUserSettings)
    i.Flash_SaveUserSettings                 0x08004c30   Section        0  key_menu.o(i.Flash_SaveUserSettings)
    i.Force_I2C_Reset                        0x08004cc8   Section        0  key_menu.o(i.Force_I2C_Reset)
    i.HAL_DMA_Abort                          0x08004db4   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08004dfc   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x08004e94   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_FLASHEx_Erase                      0x08004eb8   Section        0  stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    i.HAL_FLASH_Lock                         0x08004f60   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    i.HAL_FLASH_Program                      0x08004f74   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Program)
    i.HAL_FLASH_Unlock                       0x08004ff4   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    i.HAL_GPIO_DeInit                        0x0800501c   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    i.HAL_GPIO_EXTI_Callback                 0x08005134   Section        0  key_menu.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080052b4   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080052cc   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080054ac   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080054b6   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080054c0   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_DeInit                         0x080054cc   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit)
    i.HAL_I2C_GetState                       0x080054fe   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState)
    i.HAL_I2C_Init                           0x08005504   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Transmit                0x0800568c   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspDeInit                      0x080057b8   Section        0  i2c.o(i.HAL_I2C_MspDeInit)
    i.HAL_I2C_MspInit                        0x080057f0   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x0800584c   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800585c   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08005880   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080058c0   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080058fc   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08005918   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08005958   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x0800597c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08005aa8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08005ac8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08005ae8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08005b34   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08005e54   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08005e7c   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08005e7e   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08005e80   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08005ee8   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08005f44   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08005fcc   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Base_Stop_IT                   0x08006024   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    i.HAL_TIM_ConfigClockSource              0x08006054   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_IC_CaptureCallback             0x08006130   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08006132   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08006262   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08006264   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08006266   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08006268   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x0800626a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_AbortReceiveCpltCallback      0x0800626c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback)
    i.HAL_UART_AbortReceive_IT               0x08006270   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT)
    i.HAL_UART_ErrorCallback                 0x08006308   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800630c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08006578   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080065dc   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080066b4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x080066d0   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08006734   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080067d4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080067d6   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x080067d8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x080067d9   Thumb Code    46  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterRequestWrite                 0x08006808   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x08006809   Thumb Code   150  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x080068a4   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x080068a5   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x080068fc   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x080068fd   Thumb Code   144  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x0800698c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800698d   Thumb Code   188  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08006a48   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08006a49   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.KeyMenu_HandleKeyPress                 0x08006aa0   Section        0  key_menu.o(i.KeyMenu_HandleKeyPress)
    i.KeyMenu_Init                           0x08006bf8   Section        0  key_menu.o(i.KeyMenu_Init)
    i.KeyMenu_Process                        0x08006df0   Section        0  key_menu.o(i.KeyMenu_Process)
    i.MX_GPIO_Init                           0x08006e18   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08006ef4   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_TIM1_Init                           0x08006f34   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08006f9c   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08007004   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_USART1_UART_Init                    0x0800706c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x080070a4   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x080070dc   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.Menu_ChangeState                       0x080070e0   Section        0  key_menu.o(i.Menu_ChangeState)
    i.Menu_Display                           0x0800717c   Section        0  key_menu.o(i.Menu_Display)
    i.Menu_HandleInput                       0x08007274   Section        0  key_menu.o(i.Menu_HandleInput)
    i.Menu_ShowCityInput                     0x08007614   Section        0  key_menu.o(i.Menu_ShowCityInput)
    i.Menu_ShowCityQuick                     0x08007698   Section        0  key_menu.o(i.Menu_ShowCityQuick)
    i.Menu_ShowCitySetting                   0x08007788   Section        0  key_menu.o(i.Menu_ShowCitySetting)
    i.Menu_ShowMainMenu                      0x080078a4   Section        0  key_menu.o(i.Menu_ShowMainMenu)
    i.Menu_ShowSystemInfo                    0x08007b18   Section        0  key_menu.o(i.Menu_ShowSystemInfo)
    i.Menu_ShowWeatherDisplay                0x08007c6c   Section        0  key_menu.o(i.Menu_ShowWeatherDisplay)
    i.NMI_Handler                            0x08007d44   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.OLED_CheckBufferIntegrity              0x08007d46   Section        0  oled.o(i.OLED_CheckBufferIntegrity)
    i.OLED_Clear                             0x08007d60   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08007d88   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_Init                              0x08007de4   Section        0  oled.o(i.OLED_Init)
    i.OLED_PowerOnInit                       0x08007e9a   Section        0  oled.o(i.OLED_PowerOnInit)
    i.OLED_SetCursor                         0x08007eb8   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x08007edc   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08007f18   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08007fd4   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_ShowWeather                       0x08008000   Section        0  oled.o(i.OLED_ShowWeather)
    i.OLED_Update                            0x08008274   Section        0  oled.o(i.OLED_Update)
    i.OLED_WriteCommand                      0x080082dc   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08008308   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08008348   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x0800834a   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.Safe_OLED_Update                       0x0800834c   Section        0  key_menu.o(i.Safe_OLED_Update)
    i.SysTick_Handler                        0x080083b4   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080083b8   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08008416   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM1_DebounceCallback                  0x08008418   Section        0  key_menu.o(i.TIM1_DebounceCallback)
    i.TIM1_UP_IRQHandler                     0x0800848c   Section        0  stm32f1xx_it.o(i.TIM1_UP_IRQHandler)
    i.TIM2_IRQHandler                        0x080084a0   Section        0  stm32f1xx_it.o(i.TIM2_IRQHandler)
    i.TIM3_IRQHandler                        0x080084ac   Section        0  stm32f1xx_it.o(i.TIM3_IRQHandler)
    i.TIM3_TimeoutCallback                   0x080084c0   Section        0  key_menu.o(i.TIM3_TimeoutCallback)
    i.TIM_Base_SetConfig                     0x08008518   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08008590   Section        0  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080085a4   Section        0  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080085a5   Thumb Code    16  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x080085b4   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x080085b5   Thumb Code    34  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x080085d6   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x080085d7   Thumb Code    36  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x080085fa   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080085fb   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMARxOnlyAbortCallback            0x0800860a   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback)
    UART_DMARxOnlyAbortCallback              0x0800860b   Thumb Code    22  stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback)
    i.UART_EndRxTransfer                     0x08008620   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08008621   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x0800866e   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800866f   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08008730   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08008731   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x080087e8   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x0800881e   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x0800881f   Thumb Code   114  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08008890   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x0800889c   Section        0  stm32f1xx_it.o(i.USART2_IRQHandler)
    i.UpdateWeatherData                      0x080088a8   Section        0  main.o(i.UpdateWeatherData)
    i.UsageFault_Handler                     0x08008b08   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08008b0a   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08008b32   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08008b33   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__mathlib_dbl_overflow                 0x08008b52   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08008b60   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x08008b70   Section        0  narrow.o(i.__mathlib_narrow)
    i.__mathlib_tofloat                      0x08008b80   Section        0  narrow.o(i.__mathlib_tofloat)
    i.__support_ldexp                        0x08008c08   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08008c1e   Section        0  __printf_wp.o(i._is_digit)
    i.atof                                   0x08008c2c   Section        0  atof.o(i.atof)
    i.frexp                                  0x08008c58   Section        0  frexp.o(i.frexp)
    i.ldexp                                  0x08008cb0   Section        0  ldexp.o(i.ldexp)
    i.main                                   0x08008d24   Section        0  main.o(i.main)
    locale$$code                             0x080090f0   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x0800911c   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08009148   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dcheck1                            0x080091ac   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x080091bc   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$deqf                               0x080091d4   Section      120  deqf.o(x$fpl$deqf)
    x$fpl$dleqf                              0x0800924c   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dnaninf                            0x080092c4   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08009360   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800936c   Section      108  drleqf.o(x$fpl$drleqf)
    x$fpl$f2d                                0x080093d8   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800942e   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080094ba   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x080094c4   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x080094c8   Section        4  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x080094cc   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08009530   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x0800958c   Section        4  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x08009590   Section        8  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x08009598   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x080095c8   Section     1520  oled_data.o(.constdata)
    x$fpl$usenofp                            0x080095c8   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08009bb8   Section      570  oled_data.o(.constdata)
    .constdata                               0x08009df4   Section     1292  key_menu.o(.constdata)
    .constdata                               0x0800a300   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x0800a300   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x0800a302   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x0800a312   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x0800a322   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x0800a32a   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800a32a   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800a33e   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800a352   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800a352   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800a364   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0800a364   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800a36c   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800a36c   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800a37f   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800a394   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800a394   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800a3d0   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800a428   Section      902  esp01s.o(.conststring)
    .conststring                             0x0800a7b0   Section      114  main.o(.conststring)
    .conststring                             0x0800a824   Section      128  key_menu.o(.conststring)
    c$$dinf                                  0x0800a8c4   Section        8  fpconst.o(c$$dinf)
    locale$$data                             0x0800a8cc   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800a8d0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800a8d8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800a8e4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800a8e6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800a8e7   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800a8e8   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800a8e8   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800a8ec   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800a8f4   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800a9f8   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       36  main.o(.data)
    last_esp_process                         0x20000020   Data           4  main.o(.data)
    .data                                    0x20000024   Section       20  key_menu.o(.data)
    last_displayed_state                     0x20000028   Data           1  key_menu.o(.data)
    last_cursor                              0x20000029   Data           1  key_menu.o(.data)
    oled_error_count                         0x2000002a   Data           1  key_menu.o(.data)
    last_weather_display_update              0x2000002c   Data           4  key_menu.o(.data)
    last_key_debug                           0x20000030   Data           4  key_menu.o(.data)
    last_oled_error                          0x20000034   Data           4  key_menu.o(.data)
    .data                                    0x20000038   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000044   Section        4  system_stm32f1xx.o(.data)
    .bss                                     0x20000048   Section     4268  esp01s.o(.bss)
    response_buffer                          0x20000cf4   Data        1024  esp01s.o(.bss)
    .bss                                     0x200010f4   Section     1153  oled.o(.bss)
    buffer                                   0x200010f4   Data         129  oled.o(.bss)
    .bss                                     0x20001578   Section       84  i2c.o(.bss)
    .bss                                     0x200015cc   Section      216  tim.o(.bss)
    .bss                                     0x200016a4   Section      144  usart.o(.bss)
    .bss                                     0x20001734   Section      156  key_menu.o(.bss)
    key_press_count                          0x20001734   Data          16  key_menu.o(.bss)
    last_interrupt_time                      0x20001744   Data          16  key_menu.o(.bss)
    .bss                                     0x200017d0   Section       32  stm32f1xx_hal_flash.o(.bss)
    .bss                                     0x200017f0   Section       96  libspace.o(.bss)
    HEAP                                     0x20001850   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20001850   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20001a50   Section     4096  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20001a50   Data        4096  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20002a50   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000161   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000167   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800016d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000173   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000179   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800017f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000185   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800018f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000195   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800019b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001a1   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001a7   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080001ad   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080001b3   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080001b9   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080001bf   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080001c5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080001cb   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080001d5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080001db   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080001e1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080001e7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080001ed   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001f1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000211   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x08000215   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000215   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000215   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000227   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000229   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000229   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800022d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000235   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x0800024f   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x08000251   Thumb Code     0  startup_stm32f103xb.o(.text)
    __aeabi_llsr                             0x08000275   Thumb Code     0  llushr.o(.text)
    _ll_ushift_r                             0x08000275   Thumb Code    38  llushr.o(.text)
    vsnprintf                                0x0800029d   Thumb Code    48  vsnprintf.o(.text)
    __2sprintf                               0x080002d1   Thumb Code    38  __2sprintf.o(.text)
    __2snprintf                              0x080002fd   Thumb Code    50  __2snprintf.o(.text)
    _printf_pre_padding                      0x08000335   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000361   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000383   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080003d5   Thumb Code   104  _printf_dec.o(.text)
    _printf_longlong_hex                     0x0800044d   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x080004a3   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x080004bf   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x080004cb   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x080004e1   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    atoi                                     0x08000669   Thumb Code    26  atoi.o(.text)
    strchr                                   0x08000683   Thumb Code    20  strchr.o(.text)
    strstr                                   0x08000697   Thumb Code    36  strstr.o(.text)
    strcpy                                   0x080006bb   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x08000703   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x08000741   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000741   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080007a7   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x080007cb   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080007cb   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080007cb   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000813   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x0800082f   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x0800082f   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000833   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000873   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000873   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000873   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000877   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x080008c1   Thumb Code    86  strncpy.o(.text)
    strcmp                                   0x08000919   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000999   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800099b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800099d   Thumb Code     2  heapauxi.o(.text)
    __aeabi_errno_addr                       0x080009a1   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080009a1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080009a1   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x080009a9   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080009b3   Thumb Code    12  _rserrno.o(.text)
    _printf_truncate_signed                  0x080009bf   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080009d1   Thumb Code    18  _printf_truncate.o(.text)
    _printf_int_common                       0x080009e3   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x08000a95   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x08000abd   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000c6b   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000ee3   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000f09   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000f13   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000f23   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000f37   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000f47   Thumb Code     8  _printf_char.o(.text)
    _printf_wctomb                           0x08000f51   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x0800100d   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08001089   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x080010cd   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x080010e5   Thumb Code    12  _printf_oct_int_ll.o(.text)
    __strtod_int                             0x08001135   Thumb Code    88  strtod.o(.text)
    strtol                                   0x08001199   Thumb Code   112  strtol.o(.text)
    __user_libspace                          0x08001209   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001209   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001209   Thumb Code     0  libspace.o(.text)
    __rt_ctype_table                         0x08001211   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001221   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08001229   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x080012b3   Thumb Code    18  isspace.o(.text)
    _printf_fp_hex_real                      0x080012c5   Thumb Code   754  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x080015c1   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_lcs_common                       0x08001641   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001655   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001665   Thumb Code     8  _printf_wchar.o(.text)
    _sgetc                                   0x0800166d   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x0800168b   Thumb Code    34  _sgetc.o(.text)
    _strtoul                                 0x080016ad   Thumb Code   158  _strtoul.o(.text)
    _btod_etento                             0x0800174d   Thumb Code   216  bigflt0.o(.text)
    _wcrtomb                                 0x08001829   Thumb Code    64  _wcrtomb.o(.text)
    __user_setup_stackheap                   0x08001869   Thumb Code    74  sys_stackheap_outer.o(.text)
    _chval                                   0x080018b3   Thumb Code    28  _chval.o(.text)
    _scanf_really_real                       0x08001acd   Thumb Code   668  scanf_fp.o(.text)
    exit                                     0x08001d75   Thumb Code    12  exit.o(.text)
    _scanf_really_hex_real                   0x08001d81   Thumb Code   778  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x08002099   Thumb Code   292  scanf_infnan.o(.text)
    _sys_exit                                0x080021cd   Thumb Code     8  sys_exit.o(.text)
    __aeabi_llsl                             0x080021d9   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x080021d9   Thumb Code    38  llshl.o(.text)
    __I$use$semihosting                      0x080021ff   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080021ff   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08002201   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08002201   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x0800223f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002285   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080022e5   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x0800261d   Thumb Code    80  btod.o(CL$$btod_e2d)
    _e2e                                     0x08002671   Thumb Code   198  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002737   Thumb Code    40  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x0800275f   Thumb Code    40  btod.o(CL$$btod_edivd)
    _btod_emul                               0x08002787   Thumb Code    40  btod.o(CL$$btod_emul)
    _btod_emuld                              0x080027af   Thumb Code    40  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x080027d7   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08002a1b   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    Check_I2C_Status                         0x08002a1d   Thumb Code    66  key_menu.o(i.Check_I2C_Status)
    City_GetCurrent                          0x08002af1   Thumb Code     4  key_menu.o(i.City_GetCurrent)
    City_SetCurrent                          0x08002af9   Thumb Code    48  key_menu.o(i.City_SetCurrent)
    DebugMon_Handler                         0x08002b41   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    ESP01S_ConnectWiFi                       0x08002b45   Thumb Code    96  esp01s.o(i.ESP01S_ConnectWiFi)
    ESP01S_DebugPrint                        0x08002bc9   Thumb Code    58  esp01s.o(i.ESP01S_DebugPrint)
    ESP01S_DebugPrintATCommand               0x08002c0d   Thumb Code     8  esp01s.o(i.ESP01S_DebugPrintATCommand)
    ESP01S_DebugPrintBuffer                  0x08002c25   Thumb Code   110  esp01s.o(i.ESP01S_DebugPrintBuffer)
    ESP01S_DebugPrintResponse                0x08002cc5   Thumb Code     8  esp01s.o(i.ESP01S_DebugPrintResponse)
    ESP01S_GetLocalIP                        0x08002cdd   Thumb Code    66  esp01s.o(i.ESP01S_GetLocalIP)
    ESP01S_GetWeather                        0x08002d3d   Thumb Code     4  esp01s.o(i.ESP01S_GetWeather)
    ESP01S_GetWeatherData                    0x08002d45   Thumb Code   476  esp01s.o(i.ESP01S_GetWeatherData)
    ESP01S_HTTPGetSimplified                 0x08003259   Thumb Code  1922  esp01s.o(i.ESP01S_HTTPGetSimplified)
    ESP01S_HandleHTTPRequest                 0x08003b8d   Thumb Code    94  esp01s.o(i.ESP01S_HandleHTTPRequest)
    ESP01S_InitDebug                         0x08003c0d   Thumb Code   222  esp01s.o(i.ESP01S_InitDebug)
    ESP01S_ParseJSONString                   0x08003d2d   Thumb Code    82  esp01s.o(i.ESP01S_ParseJSONString)
    ESP01S_ParseWeatherJSON                  0x08003d89   Thumb Code   816  esp01s.o(i.ESP01S_ParseWeatherJSON)
    ESP01S_ProcessResponse                   0x08004401   Thumb Code   106  esp01s.o(i.ESP01S_ProcessResponse)
    ESP01S_ProcessWeatherRequest             0x08004491   Thumb Code   196  esp01s.o(i.ESP01S_ProcessWeatherRequest)
    ESP01S_SendCommand                       0x08004595   Thumb Code   226  esp01s.o(i.ESP01S_SendCommand)
    ESP01S_SendData                          0x08004729   Thumb Code   138  esp01s.o(i.ESP01S_SendData)
    ESP01S_StartServer                       0x080047d5   Thumb Code    76  esp01s.o(i.ESP01S_StartServer)
    ESP_Test_WebServer                       0x08004859   Thumb Code    70  main.o(i.ESP_Test_WebServer)
    ESP_Test_WiFiConnect                     0x080048d5   Thumb Code   122  main.o(i.ESP_Test_WiFiConnect)
    EXTI0_IRQHandler                         0x08004a1d   Thumb Code     6  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    EXTI1_IRQHandler                         0x08004a23   Thumb Code     6  stm32f1xx_it.o(i.EXTI1_IRQHandler)
    EXTI4_IRQHandler                         0x08004a29   Thumb Code     6  stm32f1xx_it.o(i.EXTI4_IRQHandler)
    EXTI9_5_IRQHandler                       0x08004a2f   Thumb Code     6  stm32f1xx_it.o(i.EXTI9_5_IRQHandler)
    Error_Handler                            0x08004a35   Thumb Code     4  main.o(i.Error_Handler)
    FLASH_PageErase                          0x08004a5d   Thumb Code    28  stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase)
    FLASH_WaitForLastOperation               0x08004af9   Thumb Code    80  stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    Flash_CalculateChecksum                  0x08004b4d   Thumb Code    18  key_menu.o(i.Flash_CalculateChecksum)
    Flash_LoadUserSettings                   0x08004b61   Thumb Code    72  key_menu.o(i.Flash_LoadUserSettings)
    Flash_SaveUserSettings                   0x08004c31   Thumb Code   106  key_menu.o(i.Flash_SaveUserSettings)
    Force_I2C_Reset                          0x08004cc9   Thumb Code   172  key_menu.o(i.Force_I2C_Reset)
    HAL_DMA_Abort                            0x08004db5   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08004dfd   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x08004e95   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_FLASHEx_Erase                        0x08004eb9   Thumb Code   160  stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    HAL_FLASH_Lock                           0x08004f61   Thumb Code    14  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    HAL_FLASH_Program                        0x08004f75   Thumb Code   120  stm32f1xx_hal_flash.o(i.HAL_FLASH_Program)
    HAL_FLASH_Unlock                         0x08004ff5   Thumb Code    28  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    HAL_GPIO_DeInit                          0x0800501d   Thumb Code   254  stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit)
    HAL_GPIO_EXTI_Callback                   0x08005135   Thumb Code   180  key_menu.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080052b5   Thumb Code    18  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080052cd   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080054ad   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080054b7   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080054c1   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_DeInit                           0x080054cd   Thumb Code    50  stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit)
    HAL_I2C_GetState                         0x080054ff   Thumb Code     6  stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState)
    HAL_I2C_Init                             0x08005505   Thumb Code   376  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Transmit                  0x0800568d   Thumb Code   290  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspDeInit                        0x080057b9   Thumb Code    44  i2c.o(i.HAL_I2C_MspDeInit)
    HAL_I2C_MspInit                          0x080057f1   Thumb Code    80  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800584d   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800585d   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08005881   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080058c1   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080058fd   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08005919   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08005959   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x0800597d   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08005aa9   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08005ac9   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08005ae9   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08005b35   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08005e55   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08005e7d   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08005e7f   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_MasterConfigSynchronization    0x08005e81   Thumb Code    92  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08005ee9   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08005f45   Thumb Code   122  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08005fcd   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Base_Stop_IT                     0x08006025   Thumb Code    48  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT)
    HAL_TIM_ConfigClockSource                0x08006055   Thumb Code   220  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x08006131   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08006133   Thumb Code   304  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08006263   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08006265   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08006267   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08006269   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x0800626b   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_AbortReceiveCpltCallback        0x0800626d   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback)
    HAL_UART_AbortReceive_IT                 0x08006271   Thumb Code   148  stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT)
    HAL_UART_ErrorCallback                   0x08006309   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800630d   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08006579   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080065dd   Thumb Code   198  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080066b5   Thumb Code    28  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x080066d1   Thumb Code    82  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08006735   Thumb Code   160  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080067d5   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080067d7   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    KeyMenu_HandleKeyPress                   0x08006aa1   Thumb Code   144  key_menu.o(i.KeyMenu_HandleKeyPress)
    KeyMenu_Init                             0x08006bf9   Thumb Code   244  key_menu.o(i.KeyMenu_Init)
    KeyMenu_Process                          0x08006df1   Thumb Code    34  key_menu.o(i.KeyMenu_Process)
    MX_GPIO_Init                             0x08006e19   Thumb Code   202  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08006ef5   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_TIM1_Init                             0x08006f35   Thumb Code    96  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08006f9d   Thumb Code    98  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08007005   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x0800706d   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080070a5   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x080070dd   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    Menu_ChangeState                         0x080070e1   Thumb Code    62  key_menu.o(i.Menu_ChangeState)
    Menu_Display                             0x0800717d   Thumb Code   150  key_menu.o(i.Menu_Display)
    Menu_HandleInput                         0x08007275   Thumb Code   382  key_menu.o(i.Menu_HandleInput)
    Menu_ShowCityInput                       0x08007615   Thumb Code    62  key_menu.o(i.Menu_ShowCityInput)
    Menu_ShowCityQuick                       0x08007699   Thumb Code   174  key_menu.o(i.Menu_ShowCityQuick)
    Menu_ShowCitySetting                     0x08007789   Thumb Code   174  key_menu.o(i.Menu_ShowCitySetting)
    Menu_ShowMainMenu                        0x080078a5   Thumb Code   212  key_menu.o(i.Menu_ShowMainMenu)
    Menu_ShowSystemInfo                      0x08007b19   Thumb Code   190  key_menu.o(i.Menu_ShowSystemInfo)
    Menu_ShowWeatherDisplay                  0x08007c6d   Thumb Code   136  key_menu.o(i.Menu_ShowWeatherDisplay)
    NMI_Handler                              0x08007d45   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    OLED_CheckBufferIntegrity                0x08007d47   Thumb Code    24  oled.o(i.OLED_CheckBufferIntegrity)
    OLED_Clear                               0x08007d61   Thumb Code    34  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08007d89   Thumb Code    86  oled.o(i.OLED_ClearArea)
    OLED_Init                                0x08007de5   Thumb Code   182  oled.o(i.OLED_Init)
    OLED_PowerOnInit                         0x08007e9b   Thumb Code    30  oled.o(i.OLED_PowerOnInit)
    OLED_SetCursor                           0x08007eb9   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x08007edd   Thumb Code    52  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08007f19   Thumb Code   182  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08007fd5   Thumb Code    44  oled.o(i.OLED_ShowString)
    OLED_ShowWeather                         0x08008001   Thumb Code   398  oled.o(i.OLED_ShowWeather)
    OLED_Update                              0x08008275   Thumb Code    68  oled.o(i.OLED_Update)
    OLED_WriteCommand                        0x080082dd   Thumb Code    40  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08008309   Thumb Code    56  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08008349   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x0800834b   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    Safe_OLED_Update                         0x0800834d   Thumb Code    96  key_menu.o(i.Safe_OLED_Update)
    SysTick_Handler                          0x080083b5   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080083b9   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08008417   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM1_DebounceCallback                    0x08008419   Thumb Code    54  key_menu.o(i.TIM1_DebounceCallback)
    TIM1_UP_IRQHandler                       0x0800848d   Thumb Code    16  stm32f1xx_it.o(i.TIM1_UP_IRQHandler)
    TIM2_IRQHandler                          0x080084a1   Thumb Code     6  stm32f1xx_it.o(i.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x080084ad   Thumb Code    16  stm32f1xx_it.o(i.TIM3_IRQHandler)
    TIM3_TimeoutCallback                     0x080084c1   Thumb Code    40  key_menu.o(i.TIM3_TimeoutCallback)
    TIM_Base_SetConfig                       0x08008519   Thumb Code   108  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08008591   Thumb Code    20  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_IT                    0x080087e9   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08008891   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x0800889d   Thumb Code     6  stm32f1xx_it.o(i.USART2_IRQHandler)
    UpdateWeatherData                        0x080088a9   Thumb Code   252  main.o(i.UpdateWeatherData)
    UsageFault_Handler                       0x08008b09   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08008b0b   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __mathlib_dbl_overflow                   0x08008b53   Thumb Code    14  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08008b61   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x08008b71   Thumb Code    16  narrow.o(i.__mathlib_narrow)
    __mathlib_tofloat                        0x08008b81   Thumb Code   132  narrow.o(i.__mathlib_tofloat)
    __support_ldexp                          0x08008c09   Thumb Code    22  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08008c1f   Thumb Code    14  __printf_wp.o(i._is_digit)
    atof                                     0x08008c2d   Thumb Code    42  atof.o(i.atof)
    frexp                                    0x08008c59   Thumb Code    80  frexp.o(i.frexp)
    ldexp                                    0x08008cb1   Thumb Code   116  ldexp.o(i.ldexp)
    main                                     0x08008d25   Thumb Code   470  main.o(i.main)
    _get_lc_numeric                          0x080090f1   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x0800911d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08009149   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08009149   Thumb Code    98  d2f.o(x$fpl$d2f)
    __fpl_dcheck_NaN1                        0x080091ad   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x080091bd   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_cdcmpeq                          0x080091d5   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x080091d5   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_cdcmple                          0x0800924d   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x0800924d   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x080092af   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __fpl_dnaninf                            0x080092c5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08009361   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800936d   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800936d   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_f2d                              0x080093d9   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080093d9   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800942f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080094bb   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x080094c5   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x080094c9   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x080094cd   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08009531   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x0800958d   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x08009591   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x08009595   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x08009599   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    OLED_F8x16                               0x080095c8   Data        1520  oled_data.o(.constdata)
    __I$use$fp                               0x080095c8   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F6x8                                0x08009bb8   Data         570  oled_data.o(.constdata)
    PRESET_CITIES                            0x08009df4   Data         960  key_menu.o(.constdata)
    MAIN_MENU_ITEMS                          0x0800a1b4   Data          99  key_menu.o(.constdata)
    CITY_SETTING_ITEMS                       0x0800a217   Data          99  key_menu.o(.constdata)
    AHBPrescTable                            0x0800a312   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x0800a322   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x0800a8a4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a8c4   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x0800a8c4   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x0800a8c4   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x0800a8c4   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x0800a8c4   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x0800a8c4   Data           0  fpconst.o(c$$dinf)
    __ctype                                  0x0800a8f5   Data           0  lc_ctype_c.o(locale$$data)
    wifi_connected                           0x20000000   Data           1  main.o(.data)
    server_started                           0x20000001   Data           1  main.o(.data)
    weather_retry_count                      0x20000002   Data           1  main.o(.data)
    display_updating                         0x20000003   Data           1  main.o(.data)
    weather_update_requested                 0x20000004   Data           1  main.o(.data)
    display_update_requested                 0x20000005   Data           1  main.o(.data)
    weather_updating                         0x20000006   Data           1  main.o(.data)
    OPENWEATHER_API_KEY                      0x20000008   Data           4  main.o(.data)
    last_status_check                        0x2000000c   Data           4  main.o(.data)
    last_weather_update                      0x20000010   Data           4  main.o(.data)
    last_mode_switch                         0x20000014   Data           4  main.o(.data)
    last_weather_error                       0x20000018   Data           4  main.o(.data)
    last_wifi_check                          0x2000001c   Data           4  main.o(.data)
    g_current_menu_state                     0x20000024   Data           1  key_menu.o(.data)
    g_menu_cursor                            0x20000025   Data           1  key_menu.o(.data)
    g_menu_timeout_counter                   0x20000026   Data           1  key_menu.o(.data)
    g_debounce_timer_active                  0x20000027   Data           1  key_menu.o(.data)
    uwTickFreq                               0x20000038   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x2000003c   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000040   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x20000044   Data           4  system_stm32f1xx.o(.data)
    esp01s                                   0x20000048   Data        3244  esp01s.o(.bss)
    OLED_DisplayBuf                          0x20001175   Data        1024  oled.o(.bss)
    hi2c1                                    0x20001578   Data          84  i2c.o(.bss)
    htim1                                    0x200015cc   Data          72  tim.o(.bss)
    htim2                                    0x20001614   Data          72  tim.o(.bss)
    htim3                                    0x2000165c   Data          72  tim.o(.bss)
    huart1                                   0x200016a4   Data          72  usart.o(.bss)
    huart2                                   0x200016ec   Data          72  usart.o(.bss)
    g_keys                                   0x20001754   Data          80  key_menu.o(.bss)
    g_user_settings                          0x200017a4   Data          44  key_menu.o(.bss)
    pFlash                                   0x200017d0   Data          32  stm32f1xx_hal_flash.o(.bss)
    __libspace_start                         0x200017f0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20001850   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000aa40, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x0000a9f8, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x00000008   Code   RO         4176  * !!!main             c_w.l(__main.o)
    0x080000f4   0x00000034   Code   RO         4609    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x0000001a   Code   RO         4611    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x00000002   PAD
    0x08000144   0x0000001c   Code   RO         4613    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x00000000   Code   RO         4151    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x00000006   Code   RO         4275    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000166   0x00000006   Code   RO         4276    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800016c   0x00000006   Code   RO         4150    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000172   0x00000006   Code   RO         4279    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000178   0x00000006   Code   RO         4280    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800017e   0x00000006   Code   RO         4281    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000184   0x0000000a   Code   RO         4286    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800018e   0x00000006   Code   RO         4278    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000194   0x00000006   Code   RO         4148    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800019a   0x00000006   Code   RO         4149    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001a0   0x00000006   Code   RO         4277    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001a6   0x00000006   Code   RO         4147    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001ac   0x00000006   Code   RO         4283    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080001b2   0x00000006   Code   RO         4284    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080001b8   0x00000006   Code   RO         4285    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080001be   0x00000006   Code   RO         4290    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080001c4   0x00000006   Code   RO         4291    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080001ca   0x0000000a   Code   RO         4287    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080001d4   0x00000006   Code   RO         4145    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080001da   0x00000006   Code   RO         4146    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001e0   0x00000006   Code   RO         4288    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080001e6   0x00000006   Code   RO         4289    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080001ec   0x00000004   Code   RO         4282    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001f0   0x00000002   Code   RO         4438    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001f2   0x00000000   Code   RO         4440    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001f2   0x00000000   Code   RO         4442    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001f2   0x00000000   Code   RO         4445    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001f2   0x00000000   Code   RO         4447    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001f2   0x00000000   Code   RO         4449    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001f2   0x00000006   Code   RO         4450    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001f8   0x00000000   Code   RO         4452    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001f8   0x0000000c   Code   RO         4453    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000204   0x00000000   Code   RO         4454    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000204   0x00000000   Code   RO         4456    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000204   0x0000000a   Code   RO         4457    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4458    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4460    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4462    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4464    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4466    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4468    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4470    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4472    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4476    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4478    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4480    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800020e   0x00000000   Code   RO         4482    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800020e   0x00000002   Code   RO         4483    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000210   0x00000002   Code   RO         4546    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000212   0x00000000   Code   RO         4570    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x08000212   0x00000000   Code   RO         4573    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000212   0x00000000   Code   RO         4576    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000212   0x00000000   Code   RO         4578    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x08000212   0x00000000   Code   RO         4581    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000212   0x00000002   Code   RO         4582    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000214   0x00000000   Code   RO         4240    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000214   0x00000000   Code   RO         4344    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000214   0x00000006   Code   RO         4356    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800021a   0x00000000   Code   RO         4346    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800021a   0x00000004   Code   RO         4347    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800021e   0x00000000   Code   RO         4349    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800021e   0x00000008   Code   RO         4350    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000226   0x00000002   Code   RO         4486    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000228   0x00000000   Code   RO         4510    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000228   0x00000004   Code   RO         4511    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800022c   0x00000006   Code   RO         4512    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000232   0x00000002   PAD
    0x08000234   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000274   0x00000026   Code   RO         4082    .text               c_w.l(llushr.o)
    0x0800029a   0x00000002   PAD
    0x0800029c   0x00000034   Code   RO         4086    .text               c_w.l(vsnprintf.o)
    0x080002d0   0x0000002c   Code   RO         4088    .text               c_w.l(__2sprintf.o)
    0x080002fc   0x00000038   Code   RO         4090    .text               c_w.l(__2snprintf.o)
    0x08000334   0x0000004e   Code   RO         4098    .text               c_w.l(_printf_pad.o)
    0x08000382   0x00000052   Code   RO         4100    .text               c_w.l(_printf_str.o)
    0x080003d4   0x00000078   Code   RO         4102    .text               c_w.l(_printf_dec.o)
    0x0800044c   0x00000094   Code   RO         4122    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x080004e0   0x00000188   Code   RO         4142    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000668   0x0000001a   Code   RO         4152    .text               c_w.l(atoi.o)
    0x08000682   0x00000014   Code   RO         4154    .text               c_w.l(strchr.o)
    0x08000696   0x00000024   Code   RO         4156    .text               c_w.l(strstr.o)
    0x080006ba   0x00000048   Code   RO         4158    .text               c_w.l(strcpy.o)
    0x08000702   0x0000003e   Code   RO         4160    .text               c_w.l(strlen.o)
    0x08000740   0x0000008a   Code   RO         4162    .text               c_w.l(rt_memcpy_v6.o)
    0x080007ca   0x00000064   Code   RO         4164    .text               c_w.l(rt_memcpy_w.o)
    0x0800082e   0x00000044   Code   RO         4166    .text               c_w.l(rt_memclr.o)
    0x08000872   0x0000004e   Code   RO         4168    .text               c_w.l(rt_memclr_w.o)
    0x080008c0   0x00000056   Code   RO         4170    .text               c_w.l(strncpy.o)
    0x08000916   0x00000002   PAD
    0x08000918   0x00000080   Code   RO         4172    .text               c_w.l(strcmpv7m.o)
    0x08000998   0x00000006   Code   RO         4174    .text               c_w.l(heapauxi.o)
    0x0800099e   0x00000002   PAD
    0x080009a0   0x00000008   Code   RO         4244    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080009a8   0x00000016   Code   RO         4246    .text               c_w.l(_rserrno.o)
    0x080009be   0x00000024   Code   RO         4248    .text               c_w.l(_printf_truncate.o)
    0x080009e2   0x000000b2   Code   RO         4250    .text               c_w.l(_printf_intcommon.o)
    0x08000a94   0x00000028   Code   RO         4252    .text               c_w.l(_printf_charcount.o)
    0x08000abc   0x0000041a   Code   RO         4254    .text               c_w.l(_printf_fp_dec.o)
    0x08000ed6   0x00000002   PAD
    0x08000ed8   0x00000030   Code   RO         4256    .text               c_w.l(_printf_char_common.o)
    0x08000f08   0x0000000a   Code   RO         4258    .text               c_w.l(_sputc.o)
    0x08000f12   0x00000010   Code   RO         4260    .text               c_w.l(_snputc.o)
    0x08000f22   0x0000002c   Code   RO         4262    .text               c_w.l(_printf_char.o)
    0x08000f4e   0x00000002   PAD
    0x08000f50   0x000000bc   Code   RO         4264    .text               c_w.l(_printf_wctomb.o)
    0x0800100c   0x0000007c   Code   RO         4267    .text               c_w.l(_printf_longlong_dec.o)
    0x08001088   0x00000070   Code   RO         4273    .text               c_w.l(_printf_oct_int_ll.o)
    0x080010f8   0x000000a0   Code   RO         4292    .text               c_w.l(strtod.o)
    0x08001198   0x00000070   Code   RO         4294    .text               c_w.l(strtol.o)
    0x08001208   0x00000008   Code   RO         4340    .text               c_w.l(libspace.o)
    0x08001210   0x00000010   Code   RO         4358    .text               c_w.l(rt_ctype_table.o)
    0x08001220   0x00000008   Code   RO         4363    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001228   0x0000008a   Code   RO         4365    .text               c_w.l(lludiv10.o)
    0x080012b2   0x00000012   Code   RO         4367    .text               c_w.l(isspace.o)
    0x080012c4   0x000002fc   Code   RO         4369    .text               c_w.l(_printf_fp_hex.o)
    0x080015c0   0x00000080   Code   RO         4372    .text               c_w.l(_printf_fp_infnan.o)
    0x08001640   0x0000002c   Code   RO         4376    .text               c_w.l(_printf_wchar.o)
    0x0800166c   0x00000040   Code   RO         4378    .text               c_w.l(_sgetc.o)
    0x080016ac   0x0000009e   Code   RO         4380    .text               c_w.l(_strtoul.o)
    0x0800174a   0x00000002   PAD
    0x0800174c   0x000000dc   Code   RO         4382    .text               c_w.l(bigflt0.o)
    0x08001828   0x00000040   Code   RO         4407    .text               c_w.l(_wcrtomb.o)
    0x08001868   0x0000004a   Code   RO         4422    .text               c_w.l(sys_stackheap_outer.o)
    0x080018b2   0x0000001c   Code   RO         4424    .text               c_w.l(_chval.o)
    0x080018ce   0x00000002   PAD
    0x080018d0   0x000004a4   Code   RO         4426    .text               c_w.l(scanf_fp.o)
    0x08001d74   0x0000000c   Code   RO         4431    .text               c_w.l(exit.o)
    0x08001d80   0x00000318   Code   RO         4516    .text               c_w.l(scanf_hexfp.o)
    0x08002098   0x00000134   Code   RO         4518    .text               c_w.l(scanf_infnan.o)
    0x080021cc   0x0000000c   Code   RO         4534    .text               c_w.l(sys_exit.o)
    0x080021d8   0x00000026   Code   RO         4538    .text               c_w.l(llshl.o)
    0x080021fe   0x00000002   Code   RO         4559    .text               c_w.l(use_no_semi.o)
    0x08002200   0x00000000   Code   RO         4561    .text               c_w.l(indicate_semi.o)
    0x08002200   0x0000003e   Code   RO         4385    CL$$btod_d2e        c_w.l(btod.o)
    0x0800223e   0x00000046   Code   RO         4387    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002284   0x00000060   Code   RO         4386    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080022e4   0x00000338   Code   RO         4395    CL$$btod_div_common  c_w.l(btod.o)
    0x0800261c   0x00000054   Code   RO         4393    CL$$btod_e2d        c_w.l(btod.o)
    0x08002670   0x000000c6   Code   RO         4392    CL$$btod_e2e        c_w.l(btod.o)
    0x08002736   0x00000028   Code   RO         4389    CL$$btod_ediv       c_w.l(btod.o)
    0x0800275e   0x00000028   Code   RO         4391    CL$$btod_edivd      c_w.l(btod.o)
    0x08002786   0x00000028   Code   RO         4388    CL$$btod_emul       c_w.l(btod.o)
    0x080027ae   0x00000028   Code   RO         4390    CL$$btod_emuld      c_w.l(btod.o)
    0x080027d6   0x00000244   Code   RO         4394    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002a1a   0x00000002   Code   RO          914    i.BusFault_Handler  stm32f1xx_it.o
    0x08002a1c   0x000000d4   Code   RO         1068    i.Check_I2C_Status  key_menu.o
    0x08002af0   0x00000008   Code   RO         1069    i.City_GetCurrent   key_menu.o
    0x08002af8   0x00000048   Code   RO         1070    i.City_SetCurrent   key_menu.o
    0x08002b40   0x00000002   Code   RO          915    i.DebugMon_Handler  stm32f1xx_it.o
    0x08002b42   0x00000002   PAD
    0x08002b44   0x00000084   Code   RO           14    i.ESP01S_ConnectWiFi  esp01s.o
    0x08002bc8   0x00000044   Code   RO           15    i.ESP01S_DebugPrint  esp01s.o
    0x08002c0c   0x00000018   Code   RO           16    i.ESP01S_DebugPrintATCommand  esp01s.o
    0x08002c24   0x000000a0   Code   RO           17    i.ESP01S_DebugPrintBuffer  esp01s.o
    0x08002cc4   0x00000018   Code   RO           18    i.ESP01S_DebugPrintResponse  esp01s.o
    0x08002cdc   0x00000060   Code   RO           20    i.ESP01S_GetLocalIP  esp01s.o
    0x08002d3c   0x00000008   Code   RO           22    i.ESP01S_GetWeather  esp01s.o
    0x08002d44   0x00000514   Code   RO           23    i.ESP01S_GetWeatherData  esp01s.o
    0x08003258   0x00000934   Code   RO           29    i.ESP01S_HTTPGetSimplified  esp01s.o
    0x08003b8c   0x00000080   Code   RO           30    i.ESP01S_HandleHTTPRequest  esp01s.o
    0x08003c0c   0x00000120   Code   RO           32    i.ESP01S_InitDebug  esp01s.o
    0x08003d2c   0x0000005c   Code   RO           34    i.ESP01S_ParseJSONString  esp01s.o
    0x08003d88   0x00000678   Code   RO           35    i.ESP01S_ParseWeatherJSON  esp01s.o
    0x08004400   0x00000090   Code   RO           36    i.ESP01S_ProcessResponse  esp01s.o
    0x08004490   0x00000104   Code   RO           37    i.ESP01S_ProcessWeatherRequest  esp01s.o
    0x08004594   0x00000194   Code   RO           39    i.ESP01S_SendCommand  esp01s.o
    0x08004728   0x000000ac   Code   RO           40    i.ESP01S_SendData   esp01s.o
    0x080047d4   0x00000084   Code   RO           42    i.ESP01S_StartServer  esp01s.o
    0x08004858   0x0000007c   Code   RO          638    i.ESP_Test_WebServer  main.o
    0x080048d4   0x00000148   Code   RO          639    i.ESP_Test_WiFiConnect  main.o
    0x08004a1c   0x00000006   Code   RO          916    i.EXTI0_IRQHandler  stm32f1xx_it.o
    0x08004a22   0x00000006   Code   RO          917    i.EXTI1_IRQHandler  stm32f1xx_it.o
    0x08004a28   0x00000006   Code   RO          918    i.EXTI4_IRQHandler  stm32f1xx_it.o
    0x08004a2e   0x00000006   Code   RO          919    i.EXTI9_5_IRQHandler  stm32f1xx_it.o
    0x08004a34   0x00000004   Code   RO          641    i.Error_Handler     main.o
    0x08004a38   0x00000024   Code   RO         2552    i.FLASH_MassErase   stm32f1xx_hal_flash_ex.o
    0x08004a5c   0x00000024   Code   RO         2557    i.FLASH_PageErase   stm32f1xx_hal_flash_ex.o
    0x08004a80   0x0000001c   Code   RO         2449    i.FLASH_Program_HalfWord  stm32f1xx_hal_flash.o
    0x08004a9c   0x0000005c   Code   RO         2450    i.FLASH_SetErrorCode  stm32f1xx_hal_flash.o
    0x08004af8   0x00000054   Code   RO         2451    i.FLASH_WaitForLastOperation  stm32f1xx_hal_flash.o
    0x08004b4c   0x00000012   Code   RO         1072    i.Flash_CalculateChecksum  key_menu.o
    0x08004b5e   0x00000002   PAD
    0x08004b60   0x000000d0   Code   RO         1073    i.Flash_LoadUserSettings  key_menu.o
    0x08004c30   0x00000098   Code   RO         1074    i.Flash_SaveUserSettings  key_menu.o
    0x08004cc8   0x000000ec   Code   RO         1075    i.Force_I2C_Reset   key_menu.o
    0x08004db4   0x00000046   Code   RO         2113    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08004dfa   0x00000002   PAD
    0x08004dfc   0x00000098   Code   RO         2114    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08004e94   0x00000024   Code   RO         1739    i.HAL_Delay         stm32f1xx_hal.o
    0x08004eb8   0x000000a8   Code   RO         2558    i.HAL_FLASHEx_Erase  stm32f1xx_hal_flash_ex.o
    0x08004f60   0x00000014   Code   RO         2455    i.HAL_FLASH_Lock    stm32f1xx_hal_flash.o
    0x08004f74   0x00000080   Code   RO         2460    i.HAL_FLASH_Program  stm32f1xx_hal_flash.o
    0x08004ff4   0x00000028   Code   RO         2462    i.HAL_FLASH_Unlock  stm32f1xx_hal_flash.o
    0x0800501c   0x00000118   Code   RO         2046    i.HAL_GPIO_DeInit   stm32f1xx_hal_gpio.o
    0x08005134   0x00000180   Code   RO         1076    i.HAL_GPIO_EXTI_Callback  key_menu.o
    0x080052b4   0x00000018   Code   RO         2048    i.HAL_GPIO_EXTI_IRQHandler  stm32f1xx_hal_gpio.o
    0x080052cc   0x000001e0   Code   RO         2049    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080054ac   0x0000000a   Code   RO         2051    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x080054b6   0x0000000a   Code   RO         2053    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080054c0   0x0000000c   Code   RO         1743    i.HAL_GetTick       stm32f1xx_hal.o
    0x080054cc   0x00000032   Code   RO         1297    i.HAL_I2C_DeInit    stm32f1xx_hal_i2c.o
    0x080054fe   0x00000006   Code   RO         1305    i.HAL_I2C_GetState  stm32f1xx_hal_i2c.o
    0x08005504   0x00000188   Code   RO         1306    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x0800568c   0x0000012c   Code   RO         1319    i.HAL_I2C_Master_Transmit  stm32f1xx_hal_i2c.o
    0x080057b8   0x00000038   Code   RO          764    i.HAL_I2C_MspDeInit  i2c.o
    0x080057f0   0x0000005c   Code   RO          765    i.HAL_I2C_MspInit   i2c.o
    0x0800584c   0x00000010   Code   RO         1749    i.HAL_IncTick       stm32f1xx_hal.o
    0x0800585c   0x00000024   Code   RO         1750    i.HAL_Init          stm32f1xx_hal.o
    0x08005880   0x00000040   Code   RO         1751    i.HAL_InitTick      stm32f1xx_hal.o
    0x080058c0   0x0000003c   Code   RO         1044    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x080058fc   0x0000001a   Code   RO         2209    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08005916   0x00000002   PAD
    0x08005918   0x00000040   Code   RO         2215    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08005958   0x00000024   Code   RO         2216    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x0800597c   0x0000012c   Code   RO         1907    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08005aa8   0x00000020   Code   RO         1914    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08005ac8   0x00000020   Code   RO         1915    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08005ae8   0x0000004c   Code   RO         1916    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08005b34   0x00000320   Code   RO         1919    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08005e54   0x00000028   Code   RO         2220    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08005e7c   0x00000002   Code   RO         3415    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08005e7e   0x00000002   Code   RO         3416    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08005e80   0x00000068   Code   RO         3434    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x08005ee8   0x0000005a   Code   RO         2711    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08005f42   0x00000002   PAD
    0x08005f44   0x00000088   Code   RO          807    i.HAL_TIM_Base_MspInit  tim.o
    0x08005fcc   0x00000058   Code   RO         2716    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08006024   0x00000030   Code   RO         2719    i.HAL_TIM_Base_Stop_IT  stm32f1xx_hal_tim.o
    0x08006054   0x000000dc   Code   RO         2720    i.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x08006130   0x00000002   Code   RO         2745    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08006132   0x00000130   Code   RO         2759    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08006262   0x00000002   Code   RO         2762    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08006264   0x00000002   Code   RO         2789    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08006266   0x00000002   Code   RO         2797    i.HAL_TIM_PeriodElapsedCallback  stm32f1xx_hal_tim.o
    0x08006268   0x00000002   Code   RO         2802    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x0800626a   0x00000002   Code   RO         3694    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x0800626c   0x00000002   Code   RO         3698    i.HAL_UART_AbortReceiveCpltCallback  stm32f1xx_hal_uart.o
    0x0800626e   0x00000002   PAD
    0x08006270   0x00000098   Code   RO         3699    i.HAL_UART_AbortReceive_IT  stm32f1xx_hal_uart.o
    0x08006308   0x00000002   Code   RO         3708    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x0800630a   0x00000002   PAD
    0x0800630c   0x0000026c   Code   RO         3711    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08006578   0x00000064   Code   RO         3712    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x080065dc   0x000000d8   Code   RO          861    i.HAL_UART_MspInit  usart.o
    0x080066b4   0x0000001c   Code   RO         3717    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x080066d0   0x00000064   Code   RO          862    i.HAL_UART_RxCpltCallback  usart.o
    0x08006734   0x000000a0   Code   RO         3720    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x080067d4   0x00000002   Code   RO         3723    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x080067d6   0x00000002   Code   RO          920    i.HardFault_Handler  stm32f1xx_it.o
    0x080067d8   0x0000002e   Code   RO         1349    i.I2C_IsAcknowledgeFailed  stm32f1xx_hal_i2c.o
    0x08006806   0x00000002   PAD
    0x08006808   0x0000009c   Code   RO         1353    i.I2C_MasterRequestWrite  stm32f1xx_hal_i2c.o
    0x080068a4   0x00000056   Code   RO         1364    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080068fa   0x00000002   PAD
    0x080068fc   0x00000090   Code   RO         1365    i.I2C_WaitOnFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x0800698c   0x000000bc   Code   RO         1366    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08006a48   0x00000056   Code   RO         1368    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08006a9e   0x00000002   PAD
    0x08006aa0   0x00000158   Code   RO         1078    i.KeyMenu_HandleKeyPress  key_menu.o
    0x08006bf8   0x000001f8   Code   RO         1079    i.KeyMenu_Init      key_menu.o
    0x08006df0   0x00000028   Code   RO         1080    i.KeyMenu_Process   key_menu.o
    0x08006e18   0x000000dc   Code   RO          740    i.MX_GPIO_Init      gpio.o
    0x08006ef4   0x00000040   Code   RO          766    i.MX_I2C1_Init      i2c.o
    0x08006f34   0x00000068   Code   RO          808    i.MX_TIM1_Init      tim.o
    0x08006f9c   0x00000068   Code   RO          809    i.MX_TIM2_Init      tim.o
    0x08007004   0x00000068   Code   RO          810    i.MX_TIM3_Init      tim.o
    0x0800706c   0x00000038   Code   RO          863    i.MX_USART1_UART_Init  usart.o
    0x080070a4   0x00000038   Code   RO          864    i.MX_USART2_UART_Init  usart.o
    0x080070dc   0x00000002   Code   RO          921    i.MemManage_Handler  stm32f1xx_it.o
    0x080070de   0x00000002   PAD
    0x080070e0   0x0000009c   Code   RO         1081    i.Menu_ChangeState  key_menu.o
    0x0800717c   0x000000f8   Code   RO         1082    i.Menu_Display      key_menu.o
    0x08007274   0x000003a0   Code   RO         1084    i.Menu_HandleInput  key_menu.o
    0x08007614   0x00000084   Code   RO         1086    i.Menu_ShowCityInput  key_menu.o
    0x08007698   0x000000f0   Code   RO         1087    i.Menu_ShowCityQuick  key_menu.o
    0x08007788   0x0000011c   Code   RO         1088    i.Menu_ShowCitySetting  key_menu.o
    0x080078a4   0x00000274   Code   RO         1089    i.Menu_ShowMainMenu  key_menu.o
    0x08007b18   0x00000154   Code   RO         1090    i.Menu_ShowSystemInfo  key_menu.o
    0x08007c6c   0x000000d8   Code   RO         1091    i.Menu_ShowWeatherDisplay  key_menu.o
    0x08007d44   0x00000002   Code   RO          922    i.NMI_Handler       stm32f1xx_it.o
    0x08007d46   0x00000018   Code   RO          351    i.OLED_CheckBufferIntegrity  oled.o
    0x08007d5e   0x00000002   PAD
    0x08007d60   0x00000028   Code   RO          352    i.OLED_Clear        oled.o
    0x08007d88   0x0000005c   Code   RO          353    i.OLED_ClearArea    oled.o
    0x08007de4   0x000000b6   Code   RO          365    i.OLED_Init         oled.o
    0x08007e9a   0x0000001e   Code   RO          368    i.OLED_PowerOnInit  oled.o
    0x08007eb8   0x00000022   Code   RO          372    i.OLED_SetCursor    oled.o
    0x08007eda   0x00000002   PAD
    0x08007edc   0x0000003c   Code   RO          374    i.OLED_ShowChar     oled.o
    0x08007f18   0x000000bc   Code   RO          377    i.OLED_ShowImage    oled.o
    0x08007fd4   0x0000002c   Code   RO          380    i.OLED_ShowString   oled.o
    0x08008000   0x00000274   Code   RO          381    i.OLED_ShowWeather  oled.o
    0x08008274   0x00000068   Code   RO          382    i.OLED_Update       oled.o
    0x080082dc   0x0000002c   Code   RO          386    i.OLED_WriteCommand  oled.o
    0x08008308   0x00000040   Code   RO          387    i.OLED_WriteData    oled.o
    0x08008348   0x00000002   Code   RO          923    i.PendSV_Handler    stm32f1xx_it.o
    0x0800834a   0x00000002   Code   RO          924    i.SVC_Handler       stm32f1xx_it.o
    0x0800834c   0x00000068   Code   RO         1092    i.Safe_OLED_Update  key_menu.o
    0x080083b4   0x00000004   Code   RO          925    i.SysTick_Handler   stm32f1xx_it.o
    0x080083b8   0x0000005e   Code   RO          644    i.SystemClock_Config  main.o
    0x08008416   0x00000002   Code   RO         4046    i.SystemInit        system_stm32f1xx.o
    0x08008418   0x00000074   Code   RO         1093    i.TIM1_DebounceCallback  key_menu.o
    0x0800848c   0x00000014   Code   RO          926    i.TIM1_UP_IRQHandler  stm32f1xx_it.o
    0x080084a0   0x0000000c   Code   RO          927    i.TIM2_IRQHandler   stm32f1xx_it.o
    0x080084ac   0x00000014   Code   RO          928    i.TIM3_IRQHandler   stm32f1xx_it.o
    0x080084c0   0x00000058   Code   RO         1094    i.TIM3_TimeoutCallback  key_menu.o
    0x08008518   0x00000078   Code   RO         2804    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08008590   0x00000014   Code   RO         2815    i.TIM_ETR_SetConfig  stm32f1xx_hal_tim.o
    0x080085a4   0x00000010   Code   RO         2816    i.TIM_ITRx_SetConfig  stm32f1xx_hal_tim.o
    0x080085b4   0x00000022   Code   RO         2822    i.TIM_TI1_ConfigInputStage  stm32f1xx_hal_tim.o
    0x080085d6   0x00000024   Code   RO         2824    i.TIM_TI2_ConfigInputStage  stm32f1xx_hal_tim.o
    0x080085fa   0x00000010   Code   RO         3725    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x0800860a   0x00000016   Code   RO         3730    i.UART_DMARxOnlyAbortCallback  stm32f1xx_hal_uart.o
    0x08008620   0x0000004e   Code   RO         3735    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x0800866e   0x000000c2   Code   RO         3737    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08008730   0x000000b8   Code   RO         3738    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x080087e8   0x00000036   Code   RO         3740    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x0800881e   0x00000072   Code   RO         3741    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08008890   0x0000000c   Code   RO          929    i.USART1_IRQHandler  stm32f1xx_it.o
    0x0800889c   0x0000000c   Code   RO          930    i.USART2_IRQHandler  stm32f1xx_it.o
    0x080088a8   0x00000260   Code   RO          645    i.UpdateWeatherData  main.o
    0x08008b08   0x00000002   Code   RO          931    i.UsageFault_Handler  stm32f1xx_it.o
    0x08008b0a   0x00000028   Code   RO         4418    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08008b32   0x00000020   Code   RO         2222    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08008b52   0x0000000e   Code   RO         4329    i.__mathlib_dbl_overflow  m_ws.l(dunder.o)
    0x08008b60   0x00000010   Code   RO         4331    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08008b70   0x00000010   Code   RO         4503    i.__mathlib_narrow  m_ws.l(narrow.o)
    0x08008b80   0x00000088   Code   RO         4504    i.__mathlib_tofloat  m_ws.l(narrow.o)
    0x08008c08   0x00000016   Code   RO         4548    i.__support_ldexp   m_ws.l(ldexp.o)
    0x08008c1e   0x0000000e   Code   RO         4135    i._is_digit         c_w.l(__printf_wp.o)
    0x08008c2c   0x0000002a   Code   RO         4235    i.atof              m_ws.l(atof.o)
    0x08008c56   0x00000002   PAD
    0x08008c58   0x00000058   Code   RO         4529    i.frexp             m_ws.l(frexp.o)
    0x08008cb0   0x00000074   Code   RO         4549    i.ldexp             m_ws.l(ldexp.o)
    0x08008d24   0x000003cc   Code   RO          646    i.main              main.o
    0x080090f0   0x0000002c   Code   RO         4410    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800911c   0x0000002c   Code   RO         4429    locale$$code        c_w.l(lc_ctype_c.o)
    0x08009148   0x00000062   Code   RO         4178    x$fpl$d2f           fz_ws.l(d2f.o)
    0x080091aa   0x00000002   PAD
    0x080091ac   0x00000010   Code   RO         4484    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x080091bc   0x00000018   Code   RO         4296    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x080091d4   0x00000078   Code   RO         4524    x$fpl$deqf          fz_ws.l(deqf.o)
    0x0800924c   0x00000078   Code   RO         4204    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x080092c4   0x0000009c   Code   RO         4298    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08009360   0x0000000c   Code   RO         4300    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x0800936c   0x0000006c   Code   RO         4208    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x080093d8   0x00000056   Code   RO         4210    x$fpl$f2d           fz_ws.l(f2d.o)
    0x0800942e   0x0000008c   Code   RO         4304    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080094ba   0x0000000a   Code   RO         4306    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080094c4   0x00000004   Code   RO         4224    x$fpl$printf1       fz_ws.l(printf1.o)
    0x080094c8   0x00000004   Code   RO         4308    x$fpl$printf2       fz_ws.l(printf2.o)
    0x080094cc   0x00000064   Code   RO         4495    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08009530   0x0000005c   Code   RO         4414    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x0800958c   0x00000004   Code   RO         4416    x$fpl$scanf1        fz_ws.l(scanf1.o)
    0x08009590   0x00000008   Code   RO         4497    x$fpl$scanf2        fz_ws.l(scanf2.o)
    0x08009598   0x00000030   Code   RO         4526    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x080095c8   0x00000000   Code   RO         4314    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080095c8   0x000005f0   Data   RO          612    .constdata          oled_data.o
    0x08009bb8   0x0000023a   Data   RO          613    .constdata          oled_data.o
    0x08009df2   0x00000002   PAD
    0x08009df4   0x0000050c   Data   RO         1096    .constdata          key_menu.o
    0x0800a300   0x00000012   Data   RO         1920    .constdata          stm32f1xx_hal_rcc.o
    0x0800a312   0x00000010   Data   RO         4047    .constdata          system_stm32f1xx.o
    0x0800a322   0x00000008   Data   RO         4048    .constdata          system_stm32f1xx.o
    0x0800a32a   0x00000028   Data   RO         4123    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800a352   0x00000011   Data   RO         4143    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800a363   0x00000001   PAD
    0x0800a364   0x00000008   Data   RO         4265    .constdata          c_w.l(_printf_wctomb.o)
    0x0800a36c   0x00000026   Data   RO         4370    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800a392   0x00000002   PAD
    0x0800a394   0x00000094   Data   RO         4383    .constdata          c_w.l(bigflt0.o)
    0x0800a428   0x00000386   Data   RO           47    .conststring        esp01s.o
    0x0800a7ae   0x00000002   PAD
    0x0800a7b0   0x00000072   Data   RO          648    .conststring        main.o
    0x0800a822   0x00000002   PAD
    0x0800a824   0x00000080   Data   RO         1100    .conststring        key_menu.o
    0x0800a8a4   0x00000020   Data   RO         4607    Region$$Table       anon$$obj.o
    0x0800a8c4   0x00000008   Data   RO         4490    c$$dinf             fz_ws.l(fpconst.o)
    0x0800a8cc   0x0000001c   Data   RO         4409    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800a8e8   0x00000110   Data   RO         4428    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00002a50, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000024   Data   RW          650    .data               main.o
    0x20000024   0x00000014   Data   RW         1101    .data               key_menu.o
    0x20000038   0x0000000c   Data   RW         1757    .data               stm32f1xx_hal.o
    0x20000044   0x00000004   Data   RW         4049    .data               system_stm32f1xx.o
    0x20000048   0x000010ac   Zero   RW           46    .bss                esp01s.o
    0x200010f4   0x00000481   Zero   RW          389    .bss                oled.o
    0x20001575   0x00000003   PAD
    0x20001578   0x00000054   Zero   RW          767    .bss                i2c.o
    0x200015cc   0x000000d8   Zero   RW          811    .bss                tim.o
    0x200016a4   0x00000090   Zero   RW          865    .bss                usart.o
    0x20001734   0x0000009c   Zero   RW         1095    .bss                key_menu.o
    0x200017d0   0x00000020   Zero   RW         2463    .bss                stm32f1xx_hal_flash.o
    0x200017f0   0x00000060   Zero   RW         4341    .bss                c_w.l(libspace.o)
    0x20001850   0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20001a50   0x00001000   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      7444       3738        902          0       4268     456166   esp01s.o
       220         18          0          0          0        919   gpio.o
       212         38          0          0         84       1941   i2c.o
      5658       2850       1420         20        156      17456   key_menu.o
      2130       1118        114         36          0       6737   main.o
      1534        304          0          0       1153       9594   oled.o
         0          0       2090          0          0        902   oled_data.o
        64         26        236          0       4608        772   startup_stm32f103xb.o
       164         28          0         12          0       5533   stm32f1xx_hal.o
       198         14          0          0          0      28363   stm32f1xx_hal_cortex.o
       222          4          0          0          0       1567   stm32f1xx_hal_dma.o
       392         46          0          0         32       4090   stm32f1xx_hal_flash.o
       240         26          0          0          0       2953   stm32f1xx_hal_flash_ex.o
       804         66          0          0          0       4272   stm32f1xx_hal_gpio.o
      1454         32          0          0          0       8773   stm32f1xx_hal_i2c.o
        60          8          0          0          0        798   stm32f1xx_hal_msp.o
      1240         84         18          0          0       4640   stm32f1xx_hal_rcc.o
       986         24          0          0          0       9768   stm32f1xx_hal_tim.o
       108         12          0          0          0       2205   stm32f1xx_hal_tim_ex.o
      1730         14          0          0          0      11055   stm32f1xx_hal_uart.o
       120         26          0          0          0       7197   stm32f1xx_it.o
         2          0         24          4          0       1007   system_stm32f1xx.o
       448         36          0          0        216       2784   tim.o
       428         52          0          0        144       2717   usart.o

    ----------------------------------------------------------------------
     25884       <USER>       <GROUP>         72      10664     592209   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          6          0          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56          6          0          0          0         88   __2snprintf.o
        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1050          0          0          0          0        216   _printf_fp_dec.o
       764         10         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112          8          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        64          0          0          0          0         92   _wcrtomb.o
        26          0          0          0          0         80   atoi.o
       220          4        148          0          0         96   bigflt0.o
      2074        132          0          0          0        940   btod.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
        38          0          0          0          0         68   llushr.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
      1188         12          0          0          0        148   scanf_fp.o
       792         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
        86          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
       160         12          0          0          0        120   strtod.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0         92   d2f.o
        16          4          0          0          0         68   dcheck1.o
        24          0          0          0          0         68   dcmpi.o
       120          4          0          0          0         92   deqf.o
       120          4          0          0          0         92   dleqf.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
        86          4          0          0          0         84   f2d.o
       140          4          0          0          0         84   fnaninf.o
         0          0          8          0          0          0   fpconst.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         4          0          0          0          0         68   printf2.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
         4          0          0          0          0         68   scanf1.o
         8          0          0          0          0         84   scanf2.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
        42          0          0          0          0         84   atof.o
        30          6          0          0          0        136   dunder.o
        40          0          0          0          0         68   fpclassify.o
        88          8          0          0          0         76   frexp.o
       138          0          0          0          0        160   ldexp.o
       152          4          0          0          0        168   narrow.o

    ----------------------------------------------------------------------
     12224        <USER>        <GROUP>          0         96       8404   Library Totals
        22          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

     10562        340        551          0         96       6332   c_w.l
      1150         28          8          0          0       1380   fz_ws.l
       490         18          0          0          0        692   m_ws.l

    ----------------------------------------------------------------------
     12224        <USER>        <GROUP>          0         96       8404   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     38108       8950       5404         72      10760     587245   Grand Totals
     38108       8950       5404         72      10760     587245   ELF Image Totals
     38108       8950       5404         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                43512 (  42.49kB)
    Total RW  Size (RW Data + ZI Data)             10832 (  10.58kB)
    Total ROM Size (Code + RO Data + RW Data)      43584 (  42.56kB)

==============================================================================

