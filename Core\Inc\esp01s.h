#ifndef __ESP01S_H__
#define __ESP01S_H__

#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "tim.h"
#include <string.h>
#include <stdio.h>

/* ESP-01S 配置参数 */
#define ESP_UART_HANDLE         huart1
#define ESP_UART_TIMEOUT        1000
#define ESP_BUFFER_SIZE         1024
#define ESP_MAX_RETRY           3

/* ESP-01S GPIO 控制引脚 */
#define ESP_EN_PIN              ESP_EN_Pin
#define ESP_EN_PORT             ESP_EN_GPIO_Port
#define ESP_RST_PIN             ESP_RST_Pin
#define ESP_RST_PORT            ESP_RST_GPIO_Port

/* OpenWeatherMap API配置 - 外部定义 */
extern const char* OPENWEATHER_API_KEY;
extern const char* OPENWEATHER_HOST;

/* ESP-01S 状态枚举 */
typedef enum {
    ESP_OK = 0,
    ESP_ERROR,
    ESP_TIMEOUT,
    ESP_BUSY,
    ESP_NOT_CONNECTED
} ESP_Status_t;

/* WiFi 连接状态 */
typedef enum {
    WIFI_DISCONNECTED = 0,
    WIFI_CONNECTED,
    WIFI_GOT_IP,
    WIFI_CONNECTING
} WiFi_Status_t;

/* 天气请求状态机 */
typedef enum {
    WEATHER_IDLE = 0,           // 空闲状态
    WEATHER_CONNECTING,         // 正在连接服务器
    WEATHER_SENDING_REQUEST,    // 正在发送HTTP请求
    WEATHER_WAITING_RESPONSE,   // 等待响应
    WEATHER_PARSING,           // 解析数据
    WEATHER_COMPLETE,          // 完成
    WEATHER_ERROR              // 错误
} WeatherRequest_State_t;

/* 天气数据结构 */
typedef struct {
    float temperature;      // 温度 (°C)
    float feels_like;       // 体感温度 (°C)
    int humidity;          // 湿度 (%)
    int pressure;          // 气压 (hPa)
    char description[32];  // 天气描述
    char main[16];         // 主要天气状况 (Rain, Snow, Clear等)
    char city[32];         // 城市名称
    uint32_t last_update;  // 最后更新时间 (HAL_GetTick())
    uint8_t valid;         // 数据有效标志
} WeatherData_t;

/* ESP-01S 结构体 */
typedef struct {
    char rx_buffer[ESP_BUFFER_SIZE];
    char tx_buffer[ESP_BUFFER_SIZE];
    uint16_t rx_index;
    uint8_t response_ready;
    uint8_t rx_char;  // 单字符接收缓冲区
    WiFi_Status_t wifi_status;
    char local_ip[16];
    char ssid[32];
    WeatherData_t weather;  // 天气数据

    // 天气请求状态机相关
    WeatherRequest_State_t weather_state;
    uint32_t weather_request_start_time;
    uint32_t last_weather_request_time;
    char weather_response_buffer[1024];
} ESP01S_t;

/* 函数声明 */
ESP_Status_t ESP01S_Init(void);
ESP_Status_t ESP01S_InitDebug(void);
ESP_Status_t ESP01S_Reset(void);
ESP_Status_t ESP01S_SendCommand(const char* cmd, const char* expected_response, uint32_t timeout);
ESP_Status_t ESP01S_ConnectWiFi(const char* ssid, const char* password);
ESP_Status_t ESP01S_DisconnectWiFi(void);
ESP_Status_t ESP01S_GetLocalIP(char* ip_buffer);
ESP_Status_t ESP01S_StartServer(uint16_t port);
ESP_Status_t ESP01S_SendData(uint8_t link_id, const char* data, uint16_t length);
WiFi_Status_t ESP01S_GetWiFiStatus(void);
void ESP01S_ProcessResponse(void);
void ESP01S_HandleHTTPRequest(uint8_t link_id);
void ESP01S_SendSimpleResponse(uint8_t link_id);

/* 天气功能函数 */
ESP_Status_t ESP01S_HTTPGet(const char* host, const char* path, char* response_buffer, uint16_t buffer_size);
ESP_Status_t ESP01S_HTTPGetSimple(const char* host, const char* path, char* response_buffer, uint16_t buffer_size);
ESP_Status_t ESP01S_HTTPGetEnhanced(const char* host, const char* path, char* response_buffer, uint16_t buffer_size);
ESP_Status_t ESP01S_HTTPGetSimplified(const char* host, const char* path, char* response_buffer, uint16_t buffer_size, int max_retries);
ESP_Status_t ESP01S_GetWeatherData(const char* city);  // 阻塞版本（保留兼容性）
int ESP01S_ParseWeatherJSON(const char* json);
WeatherData_t* ESP01S_GetWeather(void);

/* 调试功能函数 */
void ESP01S_DebugPrint(const char* format, ...);
void ESP01S_DebugPrintBuffer(const char* prefix, const char* buffer, uint16_t len);
void ESP01S_DebugPrintATCommand(const char* cmd);
void ESP01S_DebugPrintResponse(const char* response);
ESP_Status_t ESP01S_TestATCommands(void);

/* 非阻塞天气请求函数 */
void ESP01S_StartWeatherRequest(const char* city);     // 启动天气请求
void ESP01S_ProcessWeatherRequest(void);               // 处理天气请求状态机
WeatherRequest_State_t ESP01S_GetWeatherRequestState(void);  // 获取请求状态
uint8_t ESP01S_IsWeatherRequestInProgress(void);      // 检查是否正在请求中

/* 外部变量声明 */
extern ESP01S_t esp01s;

#endif /* __ESP01S_H__ */
