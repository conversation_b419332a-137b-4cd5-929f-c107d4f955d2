# 🌤️ ESP-01S 天气功能使用指南

## 🎯 功能概述

### 新增功能：
- ✅ **天气数据获取** - 通过WiFi获取实时天气信息
- ✅ **OLED天气显示** - 在屏幕上显示温度、湿度、天气状况
- ✅ **自动模式切换** - 每30秒自动在状态显示和天气显示间切换
- ✅ **定时更新** - 每15分钟自动更新天气数据
- ✅ **模拟数据测试** - 当前使用模拟数据，便于测试功能

## 📱 操作方法

### 🔄 显示模式切换
- **自动切换**：每30秒自动在两种显示模式间切换
  - **模式0**：系统状态显示（WiFi、服务器、运行时间）
  - **模式1**：天气信息显示（温度、湿度、天气状况）

### 📊 天气显示界面
```
┌─────────────────┐
│ Beijing Weather │
│                 │
│ Partly Cloudy   │
│                 │
│ Temp: 25.5 C    │
│ Humidity: 65%   │
│                 │
│ Updated: 0m ago │
└─────────────────┘
```

### 🔄 自动更新
- **更新间隔**：15分钟
- **更新提示**：显示"Getting Weather..."
- **更新时间**：显示距离上次更新的时间

## 🛠️ 技术实现

### 📡 数据获取流程
1. **WiFi连接** → 确保网络连接正常
2. **HTTP请求** → 向天气API发送GET请求
3. **JSON解析** → 提取温度、湿度、天气描述
4. **数据存储** → 保存到ESP01S结构体中
5. **OLED显示** → 更新屏幕显示

### 🔧 当前配置
- **城市**：Beijing（可在main.c中修改WEATHER_CITY）
- **更新间隔**：15分钟（可修改WEATHER_UPDATE_INTERVAL）
- **数据源**：模拟数据（用于测试）

## 🌐 真实API配置（可选）

### 申请OpenWeatherMap API Key
1. 访问：https://openweathermap.org/api
2. 注册免费账户
3. 获取API Key
4. 修改代码中的API调用

### 修改代码启用真实API
在`esp01s.c`的`ESP01S_GetWeatherData`函数中：
```c
// 注释掉模拟数据部分
// 取消注释真实API调用代码
// 将YOUR_API_KEY替换为实际的API Key
```

## 📋 测试步骤

### 第1步：编译下载
1. **编译项目**：Build → Rebuild All
2. **下载程序**：Flash → Download
3. **复位系统**：按复位按钮

### 第2步：连接WiFi
1. **等待连接**：OLED显示"WiFi: Got IP"
2. **确认网络**：IP地址正常显示

### 第3步：测试天气功能
1. **默认显示**：系统状态界面
2. **等待切换**：30秒后自动切换到天气显示
3. **观察数据**：应显示模拟天气数据
4. **再次等待**：30秒后自动切换回状态显示

### 第4步：验证更新机制
1. **等待更新**：15分钟后自动更新
2. **手动触发**：重启系统立即更新
3. **观察时间**：更新时间应该重置

## 🔍 故障排除

### 问题1：天气界面显示"No data available"
**原因**：天气数据获取失败
**解决方案**：
- 检查WiFi连接状态
- 确认模拟数据代码正确
- 重启系统重新获取

### 问题2：显示模式不切换
**原因**：自动切换机制问题
**解决方案**：
- 等待30秒观察是否自动切换
- 检查系统时钟是否正常
- 重启系统重新初始化

### 问题3：天气数据不更新
**原因**：更新机制问题
**解决方案**：
- 检查WiFi连接稳定性
- 确认更新间隔设置
- 重启系统重新初始化

## 🚀 扩展功能建议

### 1. 多城市支持
```c
// 添加城市切换功能
const char* cities[] = {"Beijing", "Shanghai", "Guangzhou"};
```

### 2. 更多天气信息
```c
// 扩展天气数据结构
typedef struct {
    float temperature;
    float feels_like;      // 体感温度
    int humidity;
    int pressure;          // 气压
    float wind_speed;      // 风速
    char description[32];
    char icon[8];          // 天气图标代码
} WeatherData_t;
```

### 3. 天气图标显示
```c
// 添加简单的天气图标
void OLED_ShowWeatherIcon(int x, int y, const char* icon);
```

### 4. 历史数据记录
```c
// 保存最近几次的天气数据
typedef struct {
    WeatherData_t history[24];  // 24小时历史
    uint8_t current_index;
} WeatherHistory_t;
```

## 📊 性能说明

### 内存使用
- **天气数据结构**：约100字节
- **HTTP缓冲区**：512字节
- **总增加内存**：约600字节

### 网络使用
- **API请求频率**：每15分钟1次
- **数据传输量**：约1KB/次
- **日流量**：约100KB

### 电源消耗
- **WiFi连接**：持续功耗
- **HTTP请求**：瞬时功耗增加
- **OLED显示**：正常功耗

## ✅ 成功标志

当天气功能正常工作时：
1. **自动切换**：每30秒自动在两种显示模式间切换
2. **天气显示**：显示温度、湿度、天气状况
3. **时间更新**：显示正确的更新时间
4. **自动刷新**：定时更新天气数据

## 🎉 项目成果

你现在拥有了一个完整的**IoT天气站**：
- ✅ **WiFi连接** - ESP-01S网络通信
- ✅ **Web服务器** - 远程访问和监控
- ✅ **天气获取** - 实时天气信息
- ✅ **本地显示** - OLED状态和天气显示
- ✅ **用户交互** - 自动切换显示模式

这是一个很棒的嵌入式IoT项目！🌟

## 📞 下一步

1. **测试基本功能** - 确认自动切换和天气显示
2. **申请API Key** - 获取真实天气数据
3. **自定义城市** - 修改为你所在的城市
4. **扩展功能** - 添加更多天气信息或城市选择

恭喜你完成了ESP-01S天气功能的开发！🎊
