# ESP-01S WiFi模块使用说明

## 硬件连接

### STM32F103 与 ESP-01S 连接
```
STM32F103      ESP-01S
---------      -------
PA9 (TX)   --> RX
PA10 (RX)  <-- TX
PC13       --> EN (使能)
PC14       --> RST (复位)
3.3V       --> VCC
GND        --> GND
```

### STM32F103 与 0.96寸OLED 连接
```
STM32F103      OLED (I2C)
---------      ----------
PB6 (SCL)  --> SCL
PB7 (SDA)  --> SDA
3.3V       --> VCC
GND        --> GND
```

### 重要注意事项
1. **电源电压**: ESP-01S 和 OLED 都必须使用 3.3V 供电，不能使用 5V
2. **电流需求**: ESP-01S 工作时电流较大，确保电源能提供足够电流
3. **天线**: 确保ESP-01S的PCB天线部分不被遮挡
4. **I2C地址**: OLED默认I2C地址为0x78 (7位地址0x3C左移1位)

## 软件配置

### 1. WiFi 配置
在 `main.c` 文件中修改以下参数：
```c
#define WIFI_SSID       "你的WiFi名称"
#define WIFI_PASSWORD   "你的WiFi密码"
#define SERVER_PORT     80
```

### 2. 编译和下载
1. 使用 Keil MDK 或其他 IDE 编译项目
2. 将程序下载到 STM32F103
3. 复位系统开始运行

## 功能说明

### 初始化流程
1. 系统启动后会自动初始化ESP-01S
2. 发送基本AT指令测试通信
3. 设置WiFi工作模式为Station模式

### WiFi连接
1. 自动连接到指定的WiFi网络
2. 获取IP地址
3. 设置全局变量 `wifi_connected = 1`

### Web服务器
1. WiFi连接成功后启动TCP服务器
2. 监听端口80
3. 可以通过浏览器访问ESP-01S的IP地址

## 状态指示

### OLED显示内容
系统会在OLED屏幕上实时显示以下信息：

**启动阶段**:
- OLED初始化状态
- ESP-01S初始化状态
- WiFi连接过程

**运行阶段** (每5秒更新):
- WiFi连接状态
- 本地IP地址
- Web服务器状态
- 系统运行时间

### 状态变量
- `wifi_connected`: WiFi连接状态
- `server_started`: Web服务器状态
- `esp01s.local_ip`: 本地IP地址
- `esp01s.ssid`: 连接的WiFi名称

## 调试方法

### 1. 使用调试器
- 在关键函数设置断点
- 查看 `esp01s` 结构体的内容
- 检查 `rx_buffer` 中的AT指令响应

### 2. 检查硬件连接
如果初始化失败，检查：
- 电源电压是否为3.3V
- 串口连接是否正确
- ESP-01S是否正常工作

### 3. AT指令测试
可以手动发送AT指令测试：
```
AT          // 测试通信
AT+GMR      // 查看固件版本
AT+CWLAP    // 扫描WiFi网络
```

## 扩展功能

### OLED显示功能 ✅
已集成OLED显示功能：
- 实时显示系统状态
- WiFi连接过程可视化
- IP地址和服务器状态显示
- 系统运行时间显示

### Web页面内容
可以在 `ESP01S_SendData()` 函数中发送HTML内容，创建简单的网页界面。

## 常见问题

### 1. 初始化失败
- 检查硬件连接
- 确认ESP-01S电源正常
- 检查串口波特率设置

### 2. WiFi连接失败
- 确认WiFi名称和密码正确
- 检查WiFi信号强度
- 确认WiFi网络支持2.4GHz

### 3. 无法访问Web服务器
- 确认WiFi连接成功
- 检查IP地址是否正确
- 确认防火墙设置

## 下一步开发

1. 添加OLED显示功能
2. 实现Web页面内容
3. 添加传感器数据采集
4. 实现远程控制功能
