# ESP-01S 网络连接测试方法

## 🔍 问题分析

虽然OLED显示"Server: Running"，但浏览器无法访问，可能的原因：

1. **ESP-01S服务器实际未启动**
2. **HTTP请求处理逻辑有问题**
3. **网络配置问题**
4. **防火墙或路由器设置**

## 🛠️ 逐步测试方法

### 第1步：基础网络测试

#### 1.1 Ping测试
```bash
# Windows命令提示符
ping **************

# 预期结果：应该能ping通
# 如果ping不通，说明网络层有问题
```

#### 1.2 端口扫描测试
```bash
# 使用telnet测试端口
telnet ************** 80

# 或使用nmap（如果有）
nmap -p 80 **************
```

### 第2步：ESP-01S状态检查

#### 2.1 AT指令测试
如果有串口调试工具，可以手动发送：
```
AT+CIPSERVER?    // 查看服务器状态
AT+CIPSTATUS     // 查看连接状态
AT+CIFSR         // 查看IP地址
```

#### 2.2 OLED调试信息
当前版本已添加调试功能：
- 系统会在OLED上显示接收到的数据
- 如果有客户端连接，会显示相关信息

### 第3步：简化测试

#### 3.1 使用手机热点测试
1. 手机开启热点
2. 修改WiFi配置连接手机热点
3. 用电脑连接同一热点测试

#### 3.2 使用不同设备测试
- 尝试用手机浏览器访问
- 尝试用不同电脑访问
- 排除单一设备问题

### 第4步：网络工具测试

#### 4.1 使用curl测试（Linux/Mac）
```bash
curl -v http://**************
# -v 参数显示详细连接过程
```

#### 4.2 使用wget测试
```bash
wget http://************** -O test.html
```

#### 4.3 使用浏览器开发者工具
1. 按F12打开开发者工具
2. 访问网址
3. 查看Network标签页的错误信息

## 🔧 可能的解决方案

### 方案1：检查路由器设置
1. **AP隔离**：某些路由器开启了AP隔离，设备间无法互访
2. **防火墙**：路由器可能阻止了某些端口
3. **MAC过滤**：检查是否有MAC地址过滤

### 方案2：修改ESP-01S配置
```c
// 在esp01s.c中添加更多调试信息
ESP_Status_t ESP01S_StartServer(uint16_t port)
{
    // 先关闭可能存在的服务器
    ESP01S_SendCommand("AT+CIPSERVER=0\r\n", "OK", 2000);
    HAL_Delay(1000);
    
    // 设置多连接模式
    if(ESP01S_SendCommand("AT+CIPMUX=1\r\n", "OK", 3000) != ESP_OK) {
        return ESP_ERROR;
    }
    
    // 启动服务器
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "AT+CIPSERVER=1,%d\r\n", port);
    if(ESP01S_SendCommand(cmd, "OK", 3000) != ESP_OK) {
        return ESP_ERROR;
    }
    
    return ESP_OK;
}
```

### 方案3：使用不同端口
```c
// 尝试使用8080端口而不是80端口
#define SERVER_PORT     8080
```
然后访问：`http://**************:8080`

### 方案4：简化HTTP响应
当前版本已经简化了HTTP响应处理，应该能工作。

## 📊 调试输出分析

### OLED显示信息含义：
- **"RX Data: +IPD,0,xxx"** - 收到HTTP请求数据
- **"RX Data: 0,CONNECT"** - 有客户端连接
- **"RX Data: 0,CLOSED"** - 客户端断开连接

### 正常的连接流程：
1. 浏览器发起连接 → OLED显示"0,CONNECT"
2. 浏览器发送HTTP请求 → OLED显示"+IPD,0,xxx"
3. ESP-01S发送响应 → 浏览器显示网页
4. 连接关闭 → OLED显示"0,CLOSED"

## 🚨 紧急测试方案

如果以上方法都不行，尝试这个最简单的测试：

### 1. 修改为回显服务器
```c
void ESP01S_SendSimpleResponse(uint8_t link_id)
{
    char response[] = "Hello from ESP-01S!";
    ESP01S_SendData(link_id, response, strlen(response));
    
    HAL_Delay(100);
    char close_cmd[32];
    sprintf(close_cmd, "AT+CIPCLOSE=%d\r\n", link_id);
    ESP01S_SendCommand(close_cmd, "OK", 1000);
}
```

### 2. 使用TCP客户端测试
下载TCP调试工具，连接到**************:80，发送任意数据看是否有响应。

## 📞 下一步行动

1. **先进行Ping测试** - 确认网络连通性
2. **观察OLED调试信息** - 看是否有连接尝试
3. **尝试不同端口** - 使用8080端口测试
4. **检查路由器设置** - 确认没有AP隔离

如果Ping都不通，问题在网络层；如果Ping通但无法访问，问题在应用层。
