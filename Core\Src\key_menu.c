#include "key_menu.h"
#include "OLED.h"
#include "esp01s.h"
#include <stdio.h>
#include <stdlib.h>

/* 全局变量定义 */
KeyInfo_t g_keys[KEY_COUNT];
MenuState_t g_current_menu_state = MENU_STATE_WEATHER_DISPLAY;
uint8_t g_menu_cursor = 0;
uint8_t g_menu_timeout_counter = 0;
uint8_t g_debounce_timer_active = 0;
UserSettings_t g_user_settings;

/* 预设城市列表 */
const CityInfo_t PRESET_CITIES[] = {
    {"Beijing", "Beijing"},
    {"Shanghai", "Shanghai"},
    {"Shenzhen", "Shenzhen"},
    {"Guangzhou", "Guangzhou"},
    {"Shanto<PERSON>", "Shantou"},
    {"Xiamen", "Xiamen"},
    {"Hangzhou", "Hangzhou"},
    {"Chengdu", "Chengdu"},
    {"Chongqing", "Chongqing"},
    {"Xi<PERSON>", "Xi<PERSON>"},
    {"Wuhan", "Wuhan"},
    {"Nanjing", "Nanjing"},
    {"Suzhou", "Suzhou"},
    {"Qingdao", "Qingdao"},
    {"Dalian", "Dalian"}
};

const uint8_t PRESET_CITIES_COUNT = sizeof(PRESET_CITIES) / sizeof(PRESET_CITIES[0]);

/* 主菜单项 */
const MenuItem_t MAIN_MENU_ITEMS[] = {
    {"City Setting", MENU_STATE_CITY_SETTING},
    {"System Info", MENU_STATE_SYSTEM_INFO},
    {"Back to Weather", MENU_STATE_WEATHER_DISPLAY}
};

const uint8_t MAIN_MENU_COUNT = sizeof(MAIN_MENU_ITEMS) / sizeof(MAIN_MENU_ITEMS[0]);

/* 城市设置菜单项 */
const MenuItem_t CITY_SETTING_ITEMS[] = {
    {"Quick Select", MENU_STATE_CITY_QUICK},
    {"Custom Input", MENU_STATE_CITY_INPUT},
    {"Back to Main", MENU_STATE_MAIN}
};

const uint8_t CITY_SETTING_COUNT = sizeof(CITY_SETTING_ITEMS) / sizeof(CITY_SETTING_ITEMS[0]);

/**
 * @brief 按键菜单系统初始化
 */
void KeyMenu_Init(void)
{
    // 初始化按键信息
    g_keys[KEY_UP].port = KEY_UP_GPIO_Port;
    g_keys[KEY_UP].pin = KEY_UP_Pin;
    
    g_keys[KEY_DOWN].port = KEY_DOWN_GPIO_Port;
    g_keys[KEY_DOWN].pin = KEY_DOWN_Pin;
    
    g_keys[KEY_ENTER].port = KEY_ENTER_GPIO_Port;
    g_keys[KEY_ENTER].pin = KEY_ENTER_Pin;
    
    g_keys[KEY_BACK].port = KEY_BACK_GPIO_Port;
    g_keys[KEY_BACK].pin = KEY_BACK_Pin;
    
    // 初始化所有按键状态
    for(int i = 0; i < KEY_COUNT; i++) {
        g_keys[i].state = KEY_STATE_RELEASED;
        g_keys[i].press_time = 0;
        g_keys[i].release_time = 0;
        g_keys[i].debounce_flag = 0;
    }
    
    // 加载用户设置
    if(Flash_LoadUserSettings() != 0) {
        // 加载失败，使用默认设置
        strcpy(g_user_settings.current_city, "Shantou");
        g_user_settings.city_index = 4; // 汕头在预设列表中的索引
        g_user_settings.is_custom = 0;
        Flash_SaveUserSettings();
    }
    
    // 启动定时器
    // HAL_TIM_Base_Start_IT(&htim1);  // 防抖定时器 - 暂时禁用，使用软件防抖
    HAL_TIM_Base_Start_IT(&htim3);  // 超时定时器

    ESP01S_DebugPrint("Key menu system initialized");
    ESP01S_DebugPrint("Current city: %s", g_user_settings.current_city);

    // 初始按键状态检测
    ESP01S_DebugPrint("=== Initial Key State Check ===");
    for(int i = 0; i < KEY_COUNT; i++) {
        GPIO_PinState pin_state = HAL_GPIO_ReadPin(g_keys[i].port, g_keys[i].pin);
        const char* key_names[] = {"KEY_UP", "KEY_DOWN", "KEY_ENTER", "KEY_BACK"};
        ESP01S_DebugPrint("%s (PA%d): %s", key_names[i],
                         g_keys[i].pin == GPIO_PIN_0 ? 0 :
                         g_keys[i].pin == GPIO_PIN_1 ? 1 :
                         g_keys[i].pin == GPIO_PIN_4 ? 4 : 5,
                         pin_state == GPIO_PIN_SET ? "HIGH (Released)" : "LOW (Pressed)");
    }
    ESP01S_DebugPrint("===============================");
    ESP01S_DebugPrint("Press any key to test...");
    ESP01S_DebugPrint("Watch OLED bottom line for real-time key states");
}

/**
 * @brief 按键菜单主处理函数
 */
void KeyMenu_Process(void)
{
    // 只在天气显示模式下更新显示，菜单模式下不自动更新
    if(g_current_menu_state == MENU_STATE_WEATHER_DISPLAY) {
        static uint32_t last_weather_display_update = 0;
        uint32_t current_time = HAL_GetTick();

        if(current_time - last_weather_display_update > 2000) { // 每2秒更新天气显示，减少频率
            Menu_Display();
            last_weather_display_update = current_time;
        }
    }
    // 菜单状态下不自动更新，只在按键时更新
}

/**
 * @brief 强制更新菜单显示（仅在必要时调用）
 */
void Menu_ForceUpdate(void)
{
    Menu_Display();
    ESP01S_DebugPrint("Menu display updated");
}



/**
 * @brief 获取按键状态
 */
KeyState_t KeyMenu_GetKeyState(KeyType_t key)
{
    if(key >= KEY_COUNT) return KEY_STATE_RELEASED;
    return g_keys[key].state;
}

/**
 * @brief 处理按键按下事件（优化版，避免阻塞）
 */
void KeyMenu_HandleKeyPress(KeyType_t key)
{
    const char* key_names[] = {"KEY_UP", "KEY_DOWN", "KEY_ENTER", "KEY_BACK"};
    const char* state_names[] = {"WEATHER_DISPLAY", "MAIN", "CITY_SETTING", "CITY_QUICK", "CITY_INPUT", "SYSTEM_INFO", "CONFIRM"};

    ESP01S_DebugPrint("=== KEY PRESS EVENT ===");
    ESP01S_DebugPrint("Key: %s (%d)", key_names[key], key);
    ESP01S_DebugPrint("Current menu state: %s (%d)", state_names[g_current_menu_state], g_current_menu_state);
    ESP01S_DebugPrint("Menu cursor: %d", g_menu_cursor);

    // 重置菜单超时计数器
    Menu_ResetTimeout();

    // 恢复完整的按键处理功能
    static uint32_t key_press_count[KEY_COUNT] = {0};
    key_press_count[key]++;

    ESP01S_DebugPrint("Key %s press count: %lu", key_names[key], key_press_count[key]);

    // 根据当前菜单状态处理按键
    Menu_HandleInput(key);

    // 立即更新显示，提高响应速度
    // 只在菜单状态下使用异步更新，天气显示状态下立即更新
    if(g_current_menu_state == MENU_STATE_WEATHER_DISPLAY) {
        // 天气显示状态，立即更新
        Menu_Display();
    } else {
        // 菜单状态，使用异步更新避免复杂显示阻塞
        extern uint8_t display_update_requested;
        display_update_requested = 1;
    }

    ESP01S_DebugPrint("After processing - Menu state: %s (%d), Cursor: %d",
                     state_names[g_current_menu_state], g_current_menu_state, g_menu_cursor);
    ESP01S_DebugPrint("======================");
}

/**
 * @brief 菜单显示函数
 */
void Menu_Display(void)
{
    static MenuState_t last_displayed_state = (MenuState_t)255; // 无效状态
    static uint8_t last_cursor = 255;

    // 只在状态或光标改变时输出调试信息
    if(g_current_menu_state != last_displayed_state || g_menu_cursor != last_cursor) {
        const char* state_names[] = {"WEATHER_DISPLAY", "MAIN", "CITY_SETTING", "CITY_QUICK", "CITY_INPUT", "SYSTEM_INFO", "CONFIRM"};
        ESP01S_DebugPrint("Displaying menu: %s (%d), cursor: %d",
                         state_names[g_current_menu_state], g_current_menu_state, g_menu_cursor);
        last_displayed_state = g_current_menu_state;
        last_cursor = g_menu_cursor;
    }

    switch(g_current_menu_state) {
        case MENU_STATE_WEATHER_DISPLAY:
            Menu_ShowWeatherDisplay();
            break;

        case MENU_STATE_MAIN:
            Menu_ShowMainMenu();
            break;

        case MENU_STATE_CITY_SETTING:
            Menu_ShowCitySetting();
            break;

        case MENU_STATE_CITY_QUICK:
            Menu_ShowCityQuick();
            break;

        case MENU_STATE_CITY_INPUT:
            Menu_ShowCityInput();
            break;

        case MENU_STATE_SYSTEM_INFO:
            Menu_ShowSystemInfo();
            break;

        default:
            ESP01S_DebugPrint("Invalid menu state: %d, resetting to weather", g_current_menu_state);
            Menu_ChangeState(MENU_STATE_WEATHER_DISPLAY);
            break;
    }
}

/**
 * @brief 菜单输入处理
 */
void Menu_HandleInput(KeyType_t key)
{
    const char* key_names[] = {"KEY_UP", "KEY_DOWN", "KEY_ENTER", "KEY_BACK"};
    ESP01S_DebugPrint("Menu_HandleInput: %s in state %d, cursor %d", key_names[key], g_current_menu_state, g_menu_cursor);

    switch(g_current_menu_state) {
        case MENU_STATE_WEATHER_DISPLAY:
            if(key == KEY_ENTER) {
                ESP01S_DebugPrint("Weather -> Main menu");
                Menu_ChangeState(MENU_STATE_MAIN);
            }
            break;

        case MENU_STATE_MAIN:
            if(key == KEY_UP) {
                if(g_menu_cursor > 0) g_menu_cursor--;
                else g_menu_cursor = MAIN_MENU_COUNT - 1;
                ESP01S_DebugPrint("Main menu UP, cursor: %d", g_menu_cursor);
            } else if(key == KEY_DOWN) {
                if(g_menu_cursor < MAIN_MENU_COUNT - 1) g_menu_cursor++;
                else g_menu_cursor = 0;
                ESP01S_DebugPrint("Main menu DOWN, cursor: %d", g_menu_cursor);
            } else if(key == KEY_ENTER) {
                ESP01S_DebugPrint("Main menu ENTER, selecting item %d", g_menu_cursor);
                Menu_ChangeState(MAIN_MENU_ITEMS[g_menu_cursor].next_state);
            } else if(key == KEY_BACK) {
                ESP01S_DebugPrint("Main menu BACK -> Weather");
                Menu_ChangeState(MENU_STATE_WEATHER_DISPLAY);
            }
            break;

        case MENU_STATE_CITY_SETTING:
            if(key == KEY_UP) {
                if(g_menu_cursor > 0) g_menu_cursor--;
                else g_menu_cursor = CITY_SETTING_COUNT - 1;
                ESP01S_DebugPrint("City setting UP, cursor: %d", g_menu_cursor);
            } else if(key == KEY_DOWN) {
                if(g_menu_cursor < CITY_SETTING_COUNT - 1) g_menu_cursor++;
                else g_menu_cursor = 0;
                ESP01S_DebugPrint("City setting DOWN, cursor: %d", g_menu_cursor);
            } else if(key == KEY_ENTER) {
                ESP01S_DebugPrint("City setting ENTER, selecting item %d", g_menu_cursor);
                Menu_ChangeState(CITY_SETTING_ITEMS[g_menu_cursor].next_state);
            } else if(key == KEY_BACK) {
                ESP01S_DebugPrint("City setting BACK -> Main");
                Menu_ChangeState(MENU_STATE_MAIN);
            }
            break;

        case MENU_STATE_CITY_QUICK:
            if(key == KEY_UP) {
                if(g_menu_cursor > 0) g_menu_cursor--;
                else g_menu_cursor = PRESET_CITIES_COUNT - 1;
                ESP01S_DebugPrint("City quick UP, cursor: %d (%s)", g_menu_cursor, PRESET_CITIES[g_menu_cursor].display_name);
            } else if(key == KEY_DOWN) {
                if(g_menu_cursor < PRESET_CITIES_COUNT - 1) g_menu_cursor++;
                else g_menu_cursor = 0;
                ESP01S_DebugPrint("City quick DOWN, cursor: %d (%s)", g_menu_cursor, PRESET_CITIES[g_menu_cursor].display_name);
            } else if(key == KEY_ENTER) {
                ESP01S_DebugPrint("City selected: %s", PRESET_CITIES[g_menu_cursor].api_name);
                City_SetCurrent(PRESET_CITIES[g_menu_cursor].api_name, 0, g_menu_cursor);

                // 先返回天气显示界面，然后异步更新天气
                Menu_ChangeState(MENU_STATE_WEATHER_DISPLAY);

                // 设置天气更新标志，让主循环处理
                extern uint8_t weather_update_requested;
                weather_update_requested = 1;
                ESP01S_DebugPrint("Weather update requested for: %s", PRESET_CITIES[g_menu_cursor].api_name);
            } else if(key == KEY_BACK) {
                ESP01S_DebugPrint("City quick BACK -> City setting");
                Menu_ChangeState(MENU_STATE_CITY_SETTING);
            }
            break;

        case MENU_STATE_SYSTEM_INFO:
            if(key == KEY_BACK) {
                ESP01S_DebugPrint("System info BACK -> Main");
                Menu_ChangeState(MENU_STATE_MAIN);
            }
            break;

        default:
            if(key == KEY_BACK) {
                ESP01S_DebugPrint("Default BACK -> Weather");
                Menu_ChangeState(MENU_STATE_WEATHER_DISPLAY);
            }
            break;
    }
}

/**
 * @brief 改变菜单状态
 */
void Menu_ChangeState(MenuState_t new_state)
{
    const char* state_names[] = {"WEATHER_DISPLAY", "MAIN", "CITY_SETTING", "CITY_QUICK", "CITY_INPUT", "SYSTEM_INFO", "CONFIRM"};

    ESP01S_DebugPrint("Menu state changing: %s (%d) -> %s (%d)",
                     state_names[g_current_menu_state], g_current_menu_state,
                     state_names[new_state], new_state);

    g_current_menu_state = new_state;
    g_menu_cursor = 0;
    Menu_ResetTimeout();

    ESP01S_DebugPrint("Menu state changed successfully to: %s (%d)", state_names[new_state], new_state);
}

/**
 * @brief 重置菜单超时计数器
 */
void Menu_ResetTimeout(void)
{
    g_menu_timeout_counter = 0;
}

/**
 * @brief 获取当前城市名称
 */
const char* City_GetCurrent(void)
{
    return g_user_settings.current_city;
}

/**
 * @brief 设置当前城市
 */
void City_SetCurrent(const char* city_name, uint8_t is_custom, uint8_t city_index)
{
    strncpy(g_user_settings.current_city, city_name, sizeof(g_user_settings.current_city) - 1);
    g_user_settings.current_city[sizeof(g_user_settings.current_city) - 1] = '\0';
    g_user_settings.is_custom = is_custom;
    g_user_settings.city_index = city_index;
    
    // 保存到Flash
    Flash_SaveUserSettings();
    
    ESP01S_DebugPrint("City changed to: %s", city_name);
}

/**
 * @brief 触发天气更新（完全异步方式）
 */
void City_TriggerWeatherUpdate(void)
{
    extern uint32_t last_weather_update;
    extern uint8_t weather_retry_count;
    extern uint8_t weather_update_requested;

    // 检查是否已经在更新中，避免重复触发
    if(weather_update_requested) {
        ESP01S_DebugPrint("Weather update already in progress, ignoring request");
        return;
    }

    // 设置更新请求标志，不立即执行任何阻塞操作
    weather_update_requested = 1;

    // 重置计时器，但不执行任何可能阻塞的操作
    last_weather_update = 0; // 强制立即更新
    weather_retry_count = 0;

    ESP01S_DebugPrint("Weather update requested for city: %s (async)", g_user_settings.current_city);
    ESP01S_DebugPrint("Update will be processed in main loop");
}

/**
 * @brief 显示天气界面
 */
void Menu_ShowWeatherDisplay(void)
{
    // 使用现有的天气显示函数
    extern WeatherData_t* ESP01S_GetWeather(void);
    WeatherData_t* weather = ESP01S_GetWeather();
    OLED_ShowWeather(weather);

    // 在天气显示界面底部显示按键状态（用于调试）
    static uint32_t last_key_debug = 0;
    uint32_t current_time = HAL_GetTick();

    if(current_time - last_key_debug > 1000) { // 每秒更新一次按键状态
        char key_status[32];

        // 读取所有按键的当前物理状态
        GPIO_PinState up_state = HAL_GPIO_ReadPin(KEY_UP_GPIO_Port, KEY_UP_Pin);
        GPIO_PinState down_state = HAL_GPIO_ReadPin(KEY_DOWN_GPIO_Port, KEY_DOWN_Pin);
        GPIO_PinState enter_state = HAL_GPIO_ReadPin(KEY_ENTER_GPIO_Port, KEY_ENTER_Pin);
        GPIO_PinState back_state = HAL_GPIO_ReadPin(KEY_BACK_GPIO_Port, KEY_BACK_Pin);

        // 显示按键状态（0=按下，1=释放）
        snprintf(key_status, sizeof(key_status), "Keys: %d%d%d%d [ENTER=Menu]",
                 up_state, down_state, enter_state, back_state);

        // 在屏幕底部显示按键状态
        OLED_ShowString(0, 56, key_status, OLED_6X8);
        OLED_Update();

        last_key_debug = current_time;

        // 同时输出到串口
        ESP01S_DebugPrint("Key states - UP:%d DOWN:%d ENTER:%d BACK:%d",
                         up_state, down_state, enter_state, back_state);
    }
}

/**
 * @brief 显示主菜单
 */
/**
 * @brief 强力重置I2C总线
 */
void Force_I2C_Reset(void)
{
    extern I2C_HandleTypeDef hi2c1;

    ESP01S_DebugPrint("Force I2C reset starting...");

    // 1. 停止所有I2C操作
    HAL_I2C_DeInit(&hi2c1);

    // 2. 硬件级重置I2C引脚
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 配置SCL和SDA为输出模式，手动控制
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;  // PB6=SCL, PB7=SDA
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 3. 手动产生9个时钟脉冲清除总线
    for(int i = 0; i < 9; i++) {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET); // SCL低
        HAL_Delay(1);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);   // SCL高
        HAL_Delay(1);
    }

    // 4. 产生STOP条件
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_RESET); // SDA低
    HAL_Delay(1);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_SET);   // SCL高
    HAL_Delay(1);
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7, GPIO_PIN_SET);   // SDA高
    HAL_Delay(5);

    // 5. 重新配置I2C引脚
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 6. 重新初始化I2C
    HAL_Delay(10);
    HAL_I2C_Init(&hi2c1);

    ESP01S_DebugPrint("Force I2C reset completed");
}

/**
 * @brief 检查并重置I2C总线状态
 */
void Check_I2C_Status(void)
{
    extern I2C_HandleTypeDef hi2c1;

    HAL_I2C_StateTypeDef i2c_state = HAL_I2C_GetState(&hi2c1);
    ESP01S_DebugPrint("I2C State: %d", i2c_state);

    if(i2c_state != HAL_I2C_STATE_READY) {
        ESP01S_DebugPrint("I2C not ready (state=%d), attempting force reset...", i2c_state);

        // 使用强力重置
        Force_I2C_Reset();

        // 检查重置后状态
        i2c_state = HAL_I2C_GetState(&hi2c1);
        ESP01S_DebugPrint("I2C State after force reset: %d", i2c_state);

        if(i2c_state != HAL_I2C_STATE_READY) {
            ESP01S_DebugPrint("I2C still not ready after force reset!");
        }
    }
}

/**
 * @brief 安全的OLED更新函数
 */
uint8_t Safe_OLED_Update(void)
{
    extern I2C_HandleTypeDef hi2c1;
    static uint8_t oled_error_count = 0;
    static uint32_t last_oled_error = 0;
    uint32_t current_time = HAL_GetTick();

    // 如果最近有严重OLED错误，跳过更新
    if(oled_error_count > 5 && (current_time - last_oled_error < 5000)) {
        return 0; // 更新失败，减少调试输出
    }

    // 快速I2C状态检查，只在必要时重置
    HAL_I2C_StateTypeDef i2c_state = HAL_I2C_GetState(&hi2c1);
    if(i2c_state != HAL_I2C_STATE_READY) {
        // 只在严重错误时才进行强制重置
        if(oled_error_count > 3) {
            Check_I2C_Status();
        }
        oled_error_count++;
        last_oled_error = current_time;
        return 0; // 跳过OLED更新
    }

    // 直接更新OLED，减少调试输出
    uint32_t update_start = HAL_GetTick();
    OLED_Update();
    uint32_t update_time = HAL_GetTick() - update_start;

    if(update_time > 100) { // 降低异常阈值到100ms
        oled_error_count++;
        last_oled_error = current_time;
        return 0; // 更新失败
    } else {
        if(oled_error_count > 0) oled_error_count--; // 逐渐恢复
        return 1; // 更新成功
    }
}

void Menu_ShowMainMenu(void)
{
    ESP01S_DebugPrint("Showing main menu, cursor: %d, max: %d", g_menu_cursor, MAIN_MENU_COUNT);

    // 安全检查光标范围
    if(g_menu_cursor >= MAIN_MENU_COUNT) {
        ESP01S_DebugPrint("ERROR: cursor out of range, resetting to 0");
        g_menu_cursor = 0;
    }

    // 串口调试显示（始终显示）
    ESP01S_DebugPrint("=== MAIN MENU ===");
    ESP01S_DebugPrint("Current selection: %s", MAIN_MENU_ITEMS[g_menu_cursor].text);
    for(int i = 0; i < MAIN_MENU_COUNT; i++) {
        if(i == g_menu_cursor) {
            ESP01S_DebugPrint("  > %s", MAIN_MENU_ITEMS[i].text);
        } else {
            ESP01S_DebugPrint("    %s", MAIN_MENU_ITEMS[i].text);
        }
    }
    ESP01S_DebugPrint("Use UP/DOWN to select, ENTER to confirm, BACK to return");

    // 尝试OLED显示
    ESP01S_DebugPrint("Updating OLED display...");

    OLED_Clear();
    OLED_ShowString(0, 0, "=== Main Menu ===", OLED_6X8);

    // 显示所有菜单项
    for(int i = 0; i < MAIN_MENU_COUNT; i++) {
        char menu_line[32];
        if(i == g_menu_cursor) {
            snprintf(menu_line, sizeof(menu_line), "> %s", MAIN_MENU_ITEMS[i].text);
        } else {
            snprintf(menu_line, sizeof(menu_line), "  %s", MAIN_MENU_ITEMS[i].text);
        }
        OLED_ShowString(0, 16 + i * 10, menu_line, OLED_6X8);
    }

    OLED_ShowString(0, 56, "UP/DN Enter Back", OLED_6X8);

    // 安全的OLED更新
    if(Safe_OLED_Update()) {
        ESP01S_DebugPrint("Main menu OLED display completed successfully");
    } else {
        ESP01S_DebugPrint("Main menu OLED display failed, using text mode");
    }

    ESP01S_DebugPrint("Main menu display completed");
}

/**
 * @brief 显示城市设置菜单
 */
void Menu_ShowCitySetting(void)
{
    OLED_Clear();

    // 标题
    OLED_ShowString(0, 0, "=== City Setting ===", OLED_6X8);

    // 当前城市
    char current_info[32];
    snprintf(current_info, sizeof(current_info), "Current: %s",
             g_user_settings.is_custom ? g_user_settings.current_city :
             PRESET_CITIES[g_user_settings.city_index].display_name);
    OLED_ShowString(0, 10, current_info, OLED_6X8);

    // 菜单项
    for(int i = 0; i < CITY_SETTING_COUNT; i++) {
        if(i == g_menu_cursor) {
            OLED_ShowString(0, 24 + i * 10, ">", OLED_6X8);
            OLED_ShowString(8, 24 + i * 10, (char*)CITY_SETTING_ITEMS[i].text, OLED_6X8);
        } else {
            OLED_ShowString(8, 24 + i * 10, (char*)CITY_SETTING_ITEMS[i].text, OLED_6X8);
        }
    }

    // 状态栏
    OLED_ShowString(0, 56, "UP/DN Enter Back", OLED_6X8);

    OLED_Update();
    ESP01S_DebugPrint("City setting display completed");
}

/**
 * @brief 显示快速选择城市菜单
 */
void Menu_ShowCityQuick(void)
{
    OLED_Clear();

    // 标题
    OLED_ShowString(0, 0, "=== Select City ===", OLED_6X8);

    // 显示城市列表（最多显示4个）
    int start_index = (g_menu_cursor / 4) * 4;
    int end_index = start_index + 4;
    if(end_index > PRESET_CITIES_COUNT) end_index = PRESET_CITIES_COUNT;

    for(int i = start_index; i < end_index; i++) {
        int display_line = i - start_index;
        if(i == g_menu_cursor) {
            OLED_ShowString(0, 16 + display_line * 10, ">", OLED_6X8);
        }

        char city_info[32];
        snprintf(city_info, sizeof(city_info), "%s",
                 PRESET_CITIES[i].display_name);
        OLED_ShowString(8, 16 + display_line * 10, city_info, OLED_6X8);
    }

    // 页面指示
    char page_info[16];
    snprintf(page_info, sizeof(page_info), "%d/%d", g_menu_cursor + 1, PRESET_CITIES_COUNT);
    OLED_ShowString(100, 0, page_info, OLED_6X8);

    // 状态栏
    OLED_ShowString(0, 56, "UP/DN Enter Back", OLED_6X8);

    OLED_Update();
}

/**
 * @brief 显示自定义输入菜单（简化版）
 */
void Menu_ShowCityInput(void)
{
    OLED_Clear();

    // 标题
    OLED_ShowString(0, 0, "=== Custom City ===", OLED_6X8);

    // 提示信息
    OLED_ShowString(0, 16, "Under Development", OLED_8X16);
    OLED_ShowString(0, 32, "Use Quick Select", OLED_6X8);

    // 状态栏
    OLED_ShowString(0, 56, "Back", OLED_6X8);

    OLED_Update();
}

/**
 * @brief 显示系统信息
 */
void Menu_ShowSystemInfo(void)
{
    OLED_Clear();

    // 标题
    OLED_ShowString(0, 0, "=== System Info ===", OLED_6X8);

    // 系统信息
    extern uint8_t wifi_connected;
    extern ESP01S_t esp01s;

    char info[32];

    // WiFi状态
    snprintf(info, sizeof(info), "WiFi: %s", wifi_connected ? "Connected" : "Disconnected");
    OLED_ShowString(0, 12, info, OLED_6X8);

    // IP地址
    if(wifi_connected) {
        snprintf(info, sizeof(info), "IP: %s", esp01s.local_ip);
        OLED_ShowString(0, 22, info, OLED_6X8);
    }

    // 运行时间
    uint32_t uptime = HAL_GetTick() / 1000;
    if(uptime < 60) {
        snprintf(info, sizeof(info), "Uptime: %lus", uptime);
    } else if(uptime < 3600) {
        snprintf(info, sizeof(info), "Uptime: %lum", uptime / 60);
    } else {
        snprintf(info, sizeof(info), "Uptime: %luh", uptime / 3600);
    }
    OLED_ShowString(0, 32, info, OLED_6X8);

    // 当前城市
    snprintf(info, sizeof(info), "City: %s", g_user_settings.current_city);
    OLED_ShowString(0, 42, info, OLED_6X8);

    // 状态栏
    OLED_ShowString(0, 56, "Back", OLED_6X8);

    OLED_Update();
}

/**
 * @brief Flash存储 - 保存用户设置
 */
uint8_t Flash_SaveUserSettings(void)
{
    // 设置魔数和校验和
    g_user_settings.magic = USER_SETTINGS_MAGIC;
    g_user_settings.checksum = Flash_CalculateChecksum(&g_user_settings);

    // 解锁Flash
    HAL_FLASH_Unlock();

    // 擦除页面
    FLASH_EraseInitTypeDef erase_init;
    uint32_t page_error;

    erase_init.TypeErase = FLASH_TYPEERASE_PAGES;
    erase_init.PageAddress = USER_SETTINGS_FLASH_ADDR;
    erase_init.NbPages = 1;

    if(HAL_FLASHEx_Erase(&erase_init, &page_error) != HAL_OK) {
        HAL_FLASH_Lock();
        return 1; // 擦除失败
    }

    // 写入数据
    uint32_t* data = (uint32_t*)&g_user_settings;
    uint32_t addr = USER_SETTINGS_FLASH_ADDR;

    for(int i = 0; i < sizeof(UserSettings_t) / 4; i++) {
        if(HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, addr, data[i]) != HAL_OK) {
            HAL_FLASH_Lock();
            return 2; // 写入失败
        }
        addr += 4;
    }

    // 锁定Flash
    HAL_FLASH_Lock();

    ESP01S_DebugPrint("User settings saved to Flash");
    return 0; // 成功
}

/**
 * @brief Flash存储 - 加载用户设置
 */
uint8_t Flash_LoadUserSettings(void)
{
    // 从Flash读取数据
    UserSettings_t* flash_settings = (UserSettings_t*)USER_SETTINGS_FLASH_ADDR;

    // 检查魔数
    if(flash_settings->magic != USER_SETTINGS_MAGIC) {
        ESP01S_DebugPrint("Flash settings: Invalid magic number");
        return 1; // 魔数错误
    }

    // 复制数据
    memcpy(&g_user_settings, flash_settings, sizeof(UserSettings_t));

    // 验证校验和
    uint32_t calculated_checksum = Flash_CalculateChecksum(&g_user_settings);
    if(g_user_settings.checksum != calculated_checksum) {
        ESP01S_DebugPrint("Flash settings: Checksum mismatch");
        return 2; // 校验和错误
    }

    ESP01S_DebugPrint("User settings loaded from Flash");
    ESP01S_DebugPrint("Loaded city: %s", g_user_settings.current_city);
    return 0; // 成功
}

/**
 * @brief 计算校验和
 */
uint32_t Flash_CalculateChecksum(const UserSettings_t* settings)
{
    uint32_t checksum = 0;
    const uint8_t* data = (const uint8_t*)settings;

    // 计算除校验和字段外的所有数据
    for(int i = 0; i < sizeof(UserSettings_t) - sizeof(uint32_t); i++) {
        checksum += data[i];
    }

    return checksum;
}

/**
 * @brief TIM1防抖定时器回调
 */
void TIM1_DebounceCallback(void)
{
    // 立即停止定时器并清除中断标志
    HAL_TIM_Base_Stop_IT(&htim1);
    __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE);

    // 强制重置定时器状态
    g_debounce_timer_active = 0;

    // 清除所有防抖标志，避免重复处理
    for(int i = 0; i < KEY_COUNT; i++) {
        g_keys[i].debounce_flag = 0;
    }

    ESP01S_DebugPrint("TIM1 callback - timer stopped and flags cleared");
}

/**
 * @brief TIM3菜单超时定时器回调
 */
void TIM3_TimeoutCallback(void)
{
    if(g_current_menu_state != MENU_STATE_WEATHER_DISPLAY) {
        g_menu_timeout_counter++;

        if(g_menu_timeout_counter >= MENU_TIMEOUT_SECONDS) {
            // 超时，返回天气显示
            Menu_ChangeState(MENU_STATE_WEATHER_DISPLAY);
            ESP01S_DebugPrint("Menu timeout, returning to weather display");
        }
    }
}

/**
 * @brief GPIO外部中断回调函数
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    static uint32_t last_interrupt_time[KEY_COUNT] = {0};
    uint32_t current_time = HAL_GetTick();

    // 确定是哪个按键
    KeyType_t key = KEY_COUNT; // 无效值
    const char* key_name = "UNKNOWN";

    switch(GPIO_Pin) {
        case KEY_UP_Pin:
            key = KEY_UP;
            key_name = "KEY_UP";
            break;
        case KEY_DOWN_Pin:
            key = KEY_DOWN;
            key_name = "KEY_DOWN";
            break;
        case KEY_ENTER_Pin:
            key = KEY_ENTER;
            key_name = "KEY_ENTER";
            break;
        case KEY_BACK_Pin:
            key = KEY_BACK;
            key_name = "KEY_BACK";
            break;
        default:
            ESP01S_DebugPrint("Unknown GPIO_Pin: 0x%04X", GPIO_Pin);
            return; // 不是我们关心的按键
    }

    // 简单的软件防抖：50ms内的重复中断忽略
    if(current_time - last_interrupt_time[key] < 50) {
        ESP01S_DebugPrint("Key %s ignored - too soon (debounce)", key_name);
        return;
    }
    last_interrupt_time[key] = current_time;

    // 读取当前按键物理状态
    GPIO_PinState pin_state = HAL_GPIO_ReadPin(g_keys[key].port, g_keys[key].pin);

    ESP01S_DebugPrint("EXTI: %s, Pin state: %s", key_name,
                     pin_state == GPIO_PIN_RESET ? "PRESSED" : "RELEASED");

    // 处理按键事件（简化逻辑）
    if(pin_state == GPIO_PIN_RESET) {
        // 按键按下 - 每次都处理，不管之前状态
        g_keys[key].state = KEY_STATE_PRESSED;
        g_keys[key].press_time = current_time;

        ESP01S_DebugPrint("Key %s PRESSED - processing", key_name);
        KeyMenu_HandleKeyPress(key);

        g_keys[key].state = KEY_STATE_HOLDING;
    } else {
        // 按键释放 - 立即重置状态
        if(g_keys[key].state != KEY_STATE_RELEASED) {
            ESP01S_DebugPrint("Key %s RELEASED", key_name);
        }
        g_keys[key].state = KEY_STATE_RELEASED;
        g_keys[key].release_time = current_time;
    }
}
