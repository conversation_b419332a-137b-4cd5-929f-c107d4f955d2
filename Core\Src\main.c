/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "esp01s.h"
#include "OLED.h"
#include "key_menu.h"
#include <stdio.h>
#include <string.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// WiFi配置参数 - 请修改为你的WiFi信息
// #define WIFI_SSID       "iPhone"               // 修改为你的WiFi名称
// #define WIFI_PASSWORD   "abcdabcd"          // 修改为你的WiFi密码

#define WIFI_SSID       "Xiaomi_8D90"               // 修改为你的WiFi名称
#define WIFI_PASSWORD   "abcd8888"          // 修改为你的WiFi密码

#define SERVER_PORT     80                    // Web服务器端口

// 天气配置参数
#define WEATHER_CITY    "Shantou"               // 天气查询城市
#define WEATHER_UPDATE_INTERVAL  300000         // 天气更新间隔 (5分钟，进一步降低WiFi模块发热)

// OpenWeatherMap API配置 - 请在这里填入你的API Key
const char* OPENWEATHER_API_KEY = "********************************";  // 替换为你的API Key
const char* OPENWEATHER_HOST = "api.openweathermap.org";
#define OPENWEATHER_PORT        80

// 状态LED定义（使用ESP_RST引脚作为状态指示）
#define STATUS_LED_PIN  ESP_RST_Pin
#define STATUS_LED_PORT ESP_RST_GPIO_Port

// 状态变量
uint8_t wifi_connected = 0;
uint8_t server_started = 0;
uint32_t last_status_check = 0;
uint8_t weather_mode = 0;  // 0=状态显示, 1=天气显示
uint32_t last_weather_update = 0;
uint32_t last_mode_switch = 0;
uint8_t weather_retry_count = 0;  // 天气API重试计数
uint32_t last_weather_error = 0;  // 上次天气错误时间
uint32_t last_wifi_check = 0;     // 上次WiFi检查时间
uint8_t display_updating = 0;     // 显示更新标志，防止冲突
uint8_t weather_update_requested = 0;  // 天气更新请求标志
uint8_t display_update_requested = 0;  // 显示更新请求标志
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void ESP_Test_WiFiScan(void);
void ESP_Test_WiFiConnect(void);
void ESP_Test_WebServer(void);
void ESP_PrintStatus(void);
void OLED_DisplayStatus(void);
void OLED_DisplayWiFiInfo(void);
void CheckWiFiConnection(void);
void UpdateWeatherData(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_TIM2_Init();
  MX_USART1_UART_Init();
  MX_I2C1_Init();
  MX_USART2_UART_Init();
  MX_TIM1_Init();
  MX_TIM3_Init();
  /* USER CODE BEGIN 2 */

  // 等待系统稳定
  HAL_Delay(1000);

  // 初始化调试串口
  ESP01S_DebugPrint("=== System Starting ===");
  ESP01S_DebugPrint("Debug UART initialized");

  // 初始化OLED显示屏
  if(OLED_PowerOnInit() == 0) {
    ESP01S_DebugPrint("OLED initialized successfully");

    // 显示简洁的启动信息
    OLED_Clear();
    OLED_ShowString(0, 16, "Weather Station", OLED_8X16);
    OLED_ShowString(0, 40, "Starting...", OLED_8X16);
    OLED_Update();
    HAL_Delay(1000);
  } else {
    // OLED初始化失败，但继续运行
    ESP01S_DebugPrint("OLED initialization failed");
    HAL_Delay(1000);
  }

  // 初始化按键菜单系统
  KeyMenu_Init();
  ESP01S_DebugPrint("Key menu system initialized");

  // 初始化ESP-01S (使用调试版本)
  ESP_Status_t init_result = ESP01S_InitDebug();
  if(init_result == ESP_OK) {
    // 通过串口输出初始化成功信息
    ESP01S_DebugPrint("ESP-01S initialized successfully");

    // 连接WiFi
    ESP_Test_WiFiConnect();

    // 启动Web服务器
    if(wifi_connected) {
      ESP_Test_WebServer();

      // 初始化时间变量
      last_status_check = HAL_GetTick();
      last_mode_switch = HAL_GetTick();
      last_wifi_check = HAL_GetTick();
      last_weather_update = HAL_GetTick();
      weather_retry_count = 0;

      // 不设置任何天气数据，保持最简单状态
    }
  } else {
    // 初始化失败，显示详细错误信息
    OLED_Clear();
    OLED_ShowString(0, 0, "ESP Init FAIL", OLED_8X16);

    if(init_result == ESP_ERROR) {
      OLED_ShowString(0, 16, "AT Cmd Error", OLED_8X16);
    } else if(init_result == ESP_TIMEOUT) {
      OLED_ShowString(0, 16, "AT Timeout", OLED_8X16);
    } else {
      OLED_ShowString(0, 16, "Unknown Error", OLED_8X16);
    }

    OLED_ShowString(0, 32, "Check:", OLED_8X16);
    OLED_ShowString(0, 40, "1.Power 3.3V", OLED_6X8);
    OLED_ShowString(0, 48, "2.UART PA9/PA10", OLED_6X8);
    OLED_ShowString(0, 56, "3.EN=PC13 RST=PC14", OLED_6X8);
    OLED_Update();

    while(1) {
      HAL_Delay(1000);
    }
  }

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // 优化处理：提高响应速度
    static uint32_t last_esp_process = 0;
    uint32_t current_time = HAL_GetTick();

    // 每100ms处理一次ESP响应，提高响应速度
    if(current_time - last_esp_process > 100) {
      ESP01S_ProcessResponse();
      ESP01S_ProcessWeatherRequest();
      last_esp_process = current_time;
    }

    // 天气数据更新管理：每1分钟更新一次，降低WiFi模块发热
    UpdateWeatherData();

    // 处理按键菜单系统
    KeyMenu_Process();

    // 处理显示更新请求（快速响应）
    if(display_update_requested && !display_updating) {
      display_updating = 1;
      display_update_requested = 0;

      // 快速显示更新，减少检查
      extern void Menu_Display(void);
      Menu_Display();

      display_updating = 0;
      // ESP01S_DebugPrint("Display updated in main loop"); // 减少调试输出
    }

    // 简化显示逻辑：每5秒更新一次OLED状态信息（仅在天气显示模式下）
    if(current_time - last_status_check > 5000 && !display_updating && g_current_menu_state == MENU_STATE_WEATHER_DISPLAY) {
      display_updating = 1; // 设置显示更新标志

      // 通过串口输出系统状态信息
      ESP01S_DebugPrint("=== System Status ===");
      ESP01S_DebugPrint("WiFi Status: %s", wifi_connected ? "Connected" : "Disconnected");
      if(wifi_connected) {
        ESP01S_DebugPrint("IP Address: %s", esp01s.local_ip);
      }
      ESP01S_DebugPrint("Server Status: %s", server_started ? "Running" : "Stopped");
      ESP01S_DebugPrint("System Uptime: %lu seconds", HAL_GetTick() / 1000);
      ESP01S_DebugPrint("Current City: %s", City_GetCurrent());

      last_status_check = current_time;
      display_updating = 0; // 清除显示更新标志
    }

    // 减少延时，提高响应速度
    HAL_Delay(50);

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief 测试WiFi扫描功能
 */
void ESP_Test_WiFiScan(void)
{
  // WiFi扫描测试
  ESP01S_SendCommand("AT+CWLAP\r\n", "OK", 10000);
}

/**
 * @brief 测试WiFi连接功能
 */
void ESP_Test_WiFiConnect(void)
{
  // 显示连接状态
  OLED_Clear();
  OLED_ShowString(0, 0, "Connecting WiFi", OLED_8X16);
  OLED_ShowString(0, 16, WIFI_SSID, OLED_8X16);
  OLED_Update();

  // 连接WiFi
  if(ESP01S_ConnectWiFi(WIFI_SSID, WIFI_PASSWORD) == ESP_OK) {
    wifi_connected = 1;

    // 通过串口输出连接成功信息
    ESP01S_DebugPrint("=== WiFi Connected Successfully ===");
    ESP01S_DebugPrint("SSID: %s", WIFI_SSID);
    ESP01S_DebugPrint("IP Address: %s", esp01s.local_ip);
    ESP01S_DebugPrint("Starting weather service...");

    // WiFi连接成功后立即获取天气数据（使用当前设置的城市）
    ESP01S_GetWeatherData(City_GetCurrent());
    last_weather_update = HAL_GetTick();
  } else {
    wifi_connected = 0;
    ESP01S_DebugPrint("=== WiFi Connection Failed ===");
    ESP01S_DebugPrint("SSID: %s", WIFI_SSID);
    ESP01S_DebugPrint("Please check WiFi credentials");
  }
}

/**
 * @brief 测试Web服务器功能
 */
void ESP_Test_WebServer(void)
{
  // 显示服务器启动状态
  OLED_ShowString(0, 32, "Starting Server", OLED_8X16);
  OLED_Update();

  // 启动Web服务器
  if(ESP01S_StartServer(SERVER_PORT) == ESP_OK) {
    server_started = 1;
    OLED_ShowString(0, 32, "Server Started ", OLED_8X16);
  } else {
    server_started = 0;
    OLED_ShowString(0, 32, "Server FAIL   ", OLED_8X16);
  }
  OLED_Update();
  HAL_Delay(2000);
}

/**
 * @brief 打印系统状态
 */
void ESP_PrintStatus(void)
{
  // 状态检查函数，稍后可以用OLED显示
  // 当前为空实现
}

/**
 * @brief OLED显示系统状态
 */
void OLED_DisplayStatus(void)
{
  // 使用静态缓冲区，避免栈分配
  static char status_buffer[32];

  OLED_Clear();

  // 显示标题
  OLED_ShowString(0, 0, "ESP-01S Status", OLED_8X16);

  // 显示WiFi状态
  switch(ESP01S_GetWiFiStatus()) {
    case WIFI_DISCONNECTED:
      OLED_ShowString(0, 16, "WiFi: Disconn", OLED_8X16);
      break;
    case WIFI_CONNECTING:
      OLED_ShowString(0, 16, "WiFi: Connecting", OLED_8X16);
      break;
    case WIFI_CONNECTED:
      OLED_ShowString(0, 16, "WiFi: Connected", OLED_8X16);
      break;
    case WIFI_GOT_IP:
      OLED_ShowString(0, 16, "WiFi: Got IP", OLED_8X16);
      break;
  }

  // 显示IP地址
  if(wifi_connected) {
    OLED_ShowString(0, 32, "IP:", OLED_6X8);
    OLED_ShowString(18, 32, esp01s.local_ip, OLED_6X8);
  }

  // 显示服务器状态
  if(server_started) {
    OLED_ShowString(0, 48, "Server: Running", OLED_6X8);
  } else {
    OLED_ShowString(0, 48, "Server: Stopped", OLED_6X8);
  }

  // 显示运行时间
  snprintf(status_buffer, sizeof(status_buffer), "Time: %lus", HAL_GetTick() / 1000);
  OLED_ShowString(0, 56, status_buffer, OLED_6X8);

  // 添加延时，确保I2C操作完成
  HAL_Delay(10);
  OLED_Update();
  HAL_Delay(10);
}

/**
 * @brief OLED显示WiFi信息
 */
void OLED_DisplayWiFiInfo(void)
{
  OLED_Clear();
  OLED_ShowString(0, 0, "WiFi Info", OLED_8X16);

  if(wifi_connected) {
    OLED_ShowString(0, 16, "SSID:", OLED_6X8);
    OLED_ShowString(30, 16, esp01s.ssid, OLED_6X8);

    OLED_ShowString(0, 32, "IP:", OLED_6X8);
    OLED_ShowString(18, 32, esp01s.local_ip, OLED_6X8);

    OLED_ShowString(0, 48, "Port: 80", OLED_6X8);
  } else {
    OLED_ShowString(0, 16, "Not Connected", OLED_8X16);
  }

  OLED_Update();
}

/**
 * @brief 更新天气数据
 */
void CheckWiFiConnection(void)
{
  // 每60秒检查一次WiFi连接状态
  if(HAL_GetTick() - last_wifi_check > 60000) {
    last_wifi_check = HAL_GetTick();

    // 发送AT命令检查WiFi状态
    if(ESP01S_SendCommand("AT+CWJAP?\r\n", "+CWJAP:", 3000) != ESP_OK) {
      // WiFi连接丢失，尝试重连
      wifi_connected = 0;

      OLED_Clear();
      OLED_ShowString(0, 16, "WiFi Reconnecting", OLED_8X16);
      OLED_Update();

      // 重新连接WiFi
      if(ESP01S_ConnectWiFi(WIFI_SSID, WIFI_PASSWORD) == ESP_OK) {
        wifi_connected = 1;

        // 重连成功后立即获取天气数据（使用当前设置的城市）
        HAL_Delay(1000);
        ESP01S_GetWeatherData(City_GetCurrent());
        last_weather_update = HAL_GetTick();
        weather_retry_count = 0; // 重置重试计数
      }
    }
  }
}

void UpdateWeatherData(void)
{
  // 检查是否需要更新天气数据
  uint8_t need_update = 0;

  if(wifi_connected) {
    // 检查是否有手动更新请求
    if(weather_update_requested) {
      need_update = 1;
      weather_update_requested = 0; // 清除请求标志
      ESP01S_DebugPrint("Processing manual weather update request");
    }
    // 首次启动或正常更新间隔（5分钟）
    else if(last_weather_update == 0 || (HAL_GetTick() - last_weather_update > WEATHER_UPDATE_INTERVAL)) {
      need_update = 1;
    }
    // 如果上次失败且重试间隔已到（30秒后重试，确保5分钟内成功）
    else if(weather_retry_count > 0 && (HAL_GetTick() - last_weather_error > 30000)) {
      need_update = 1;
    }
  }

  // 防止在显示更新期间进行天气更新，避免I2C冲突
  if(need_update && !display_updating) {
    // 通过串口输出调试信息，不在OLED上显示
    ESP01S_DebugPrint("=== Weather Update ===");
    ESP01S_DebugPrint("WiFi Status: %s", wifi_connected ? "Connected" : "Disconnected");
    ESP01S_DebugPrint("API Mode: %s", (strcmp(OPENWEATHER_API_KEY, "YOUR_API_KEY_HERE") == 0) ? "Mock" : "Real");
    ESP01S_DebugPrint("Time since last update: %lu ms", HAL_GetTick() - last_weather_update);
    if(weather_retry_count > 0) {
      ESP01S_DebugPrint("Retry attempt: %d/10", weather_retry_count);
    }

    display_updating = 1; // 设置标志，防止显示冲突

    // 尝试获取天气数据（使用当前设置的城市）
    ESP_Status_t weather_result = ESP01S_GetWeatherData(City_GetCurrent());

    if(weather_result == ESP_OK) {
      // 成功：重置重试计数，更新时间戳
      weather_retry_count = 0;
      last_weather_update = HAL_GetTick();
      last_weather_error = 0;
      ESP01S_DebugPrint("Weather data updated successfully");

      // 轻量级I2C检查：只在必要时重置
      extern I2C_HandleTypeDef hi2c1;

      // 快速检查I2C状态，只在严重错误时重置
      HAL_I2C_StateTypeDef i2c_state = HAL_I2C_GetState(&hi2c1);
      if(i2c_state == HAL_I2C_STATE_ERROR || i2c_state == HAL_I2C_STATE_ABORT) {
        ESP01S_DebugPrint("I2C error state: %d, performing quick reset", i2c_state);
        HAL_I2C_DeInit(&hi2c1);
        HAL_Delay(5);  // 进一步减少延时
        HAL_I2C_Init(&hi2c1);
      }
      // 其他状态不进行重置，减少不必要的操作
    } else {
      // 失败：增加重试计数
      weather_retry_count++;
      last_weather_error = HAL_GetTick();

      // 通过串口输出错误信息
      ESP01S_DebugPrint("Weather API call failed (attempt %d/10)", weather_retry_count);

      // 如果重试次数超过10次，等待下个正常更新周期
      if(weather_retry_count >= 10) {
        weather_retry_count = 0;
        last_weather_update = HAL_GetTick(); // 重置更新时间，等待下个周期
        ESP01S_DebugPrint("Max retries reached (10 attempts in 5 minutes), waiting for next update cycle");
      }
    }

    display_updating = 0; // 清除标志
  }
}



/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
