# ESP-01S 故障排除指南

## 问题：显示 "ESP Init FAIL"

### 可能原因和解决方案

#### 1. 硬件连接问题 ⚡

**检查电源连接**:
- ESP-01S 必须使用 **3.3V** 供电，不能用5V
- 确保电源能提供足够电流（至少300mA）
- 检查VCC和GND连接是否牢固

**检查UART连接**:
```
STM32F103      ESP-01S
---------      -------
PA9 (TX)   --> RX      ✓ 交叉连接
PA10 (RX)  <-- TX      ✓ 交叉连接
PC13       --> EN      ✓ 使能引脚
PC14       --> RST     ✓ 复位引脚
```

**常见连接错误**:
- ❌ TX连TX，RX连RX（应该交叉连接）
- ❌ 使用5V供电
- ❌ 忘记连接EN和RST引脚

#### 2. ESP-01S模块问题 📡

**检查ESP-01S状态**:
- 上电后ESP-01S的蓝色LED应该闪烁
- 如果LED不亮，检查电源和模块是否损坏
- 确保ESP-01S有AT固件（不是NodeMCU固件）

**测试ESP-01S**:
1. 用USB转TTL模块单独测试ESP-01S
2. 发送 `AT` 指令，应该收到 `OK` 响应
3. 发送 `AT+GMR` 查看固件版本

#### 3. 波特率问题 🔧

**ESP-01S默认波特率**:
- 新的ESP-01S通常是115200
- 老版本可能是9600或其他波特率

**测试不同波特率**:
```c
// 在usart.c中修改波特率
huart1.Init.BaudRate = 9600;   // 尝试9600
// 或
huart1.Init.BaudRate = 74880;  // 尝试74880
```

#### 4. 时序问题 ⏰

**增加延时**:
- 复位后等待时间不够
- AT指令发送间隔太短

**解决方案**:
```c
// 在ESP01S_InitDebug中已经增加了延时
HAL_Delay(3000); // 复位后等待3秒
```

#### 5. UART中断问题 🔄

**检查中断配置**:
- UART中断是否启用
- 接收回调函数是否正确

**当前版本已修复**:
- ✅ 启用了UART接收中断
- ✅ 添加了HAL_UART_RxCpltCallback函数
- ✅ 自动重新启动接收

## 调试步骤

### 第1步：基础硬件检查
1. 用万用表测量ESP-01S的VCC引脚，确保是3.3V
2. 检查所有连接线是否牢固
3. 确认ESP-01S上电后LED有反应

### 第2步：UART通信测试
1. 使用示波器或逻辑分析仪检查UART信号
2. 确认STM32发送的AT指令格式正确
3. 检查ESP-01S是否有响应

### 第3步：软件调试
1. 在ESP01S_SendCommand函数设置断点
2. 查看esp01s.rx_buffer的内容
3. 检查response_ready标志是否被设置

### 第4步：逐步测试
```c
// 可以在main.c中添加简单测试
HAL_UART_Transmit(&huart1, (uint8_t*)"AT\r\n", 4, 1000);
HAL_Delay(1000);
// 检查是否收到响应
```

## 常见错误信息

### "AT Cmd Error"
- ESP-01S收到AT指令但返回ERROR
- 可能是指令格式错误或模块状态异常

### "AT Timeout"
- 发送AT指令后没有收到任何响应
- 通常是硬件连接或波特率问题

## 替代测试方法

### 使用USB转TTL模块测试
1. 将ESP-01S连接到USB转TTL模块
2. 使用串口调试助手发送AT指令
3. 确认ESP-01S工作正常后再连接STM32

### 简化测试代码
```c
// 在main.c中添加简单的UART测试
void Simple_UART_Test(void)
{
    char test_msg[] = "AT\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)test_msg, strlen(test_msg), 1000);
    
    uint8_t rx_data[100];
    HAL_UART_Receive(&huart1, rx_data, 100, 3000);
    
    // 在调试器中查看rx_data内容
}
```

## 成功标志

当ESP-01S初始化成功时，OLED应该显示：
- "ESP Init OK"
- 然后尝试连接WiFi

如果仍然失败，请检查：
1. 硬件连接（最常见问题）
2. 电源电压和电流
3. ESP-01S模块本身是否正常

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 硬件连接照片
2. 串口调试信息
3. ESP-01S型号和固件版本
4. 电源规格
