# 调试串口使用说明

## 配置信息
- **调试串口**：USART2
- **引脚**：PA2 (TX), PA3 (RX)
- **波特率**：115200
- **数据位**：8
- **停止位**：1
- **校验位**：无

## 连接方法

### 硬件连接
1. **PA2 (TX)** → USB转串口模块的RX
2. **PA3 (RX)** → USB转串口模块的TX
3. **GND** → USB转串口模块的GND

### 软件设置
1. 打开串口调试助手（如：串口调试精灵、SSCOM等）
2. 选择对应的COM口
3. 设置波特率：115200
4. 数据位：8
5. 停止位：1
6. 校验位：无

## 调试输出内容

### 系统启动信息
```
=== System Starting ===
Debug UART initialized
OLED initialized successfully
```

### AT命令执行过程
```
>>> AT CMD: AT+CIPMUX=0
<<< RESP: OK
>>> SUCCESS: Found expected response 'OK'
```

### HTTP请求过程
```
=== Starting HTTP request ===
Host: api.openweathermap.org
Path: /data/2.5/weather?q=Beijing&appid=b2a509e1f015c8e35513d09a136e3cc4&units=metric
=== HTTPGetEnhanced: Step 1 - Close existing connections ===
=== HTTPGetEnhanced: Step 2 - Set single connection mode ===
=== HTTPGetEnhanced: Step 3 - Establish TCP connection ===
```

### 错误信息
```
>>> ERROR: Command failed
>>> TIMEOUT: Command timed out after 20000 ms
>>> BUSY: Module busy, retrying...
```

## 调试步骤

### 第一步：连接调试串口
1. 按照上述方法连接硬件
2. 打开串口调试助手
3. 设置正确的参数

### 第二步：编译并下载程序
1. 在MDK-ARM中编译项目
2. 下载到STM32开发板

### 第三步：观察调试输出
1. **系统启动**：应该看到系统启动信息
2. **WiFi连接**：观察WiFi连接过程
3. **HTTP请求**：观察每个AT命令的执行
4. **错误定位**：根据错误信息定位问题

## 预期结果

### 正常情况
```
=== System Starting ===
Debug UART initialized
OLED initialized successfully
ESP-01S initialized successfully
WiFi connected: Xiaomi_8D90
IP address: **************
=== Starting HTTP request ===
>>> AT CMD: AT+CIPCLOSE
<<< RESP: OK
>>> SUCCESS: Found expected response 'OK'
>>> AT CMD: AT+CIPMUX=0
<<< RESP: OK
>>> SUCCESS: Found expected response 'OK'
>>> AT CMD: AT+CIPSTART="TCP","api.openweathermap.org",80
<<< RESP: CONNECT
>>> SUCCESS: Found expected response 'CONNECT'
```

### 错误情况
```
>>> AT CMD: AT+CIPSTART="TCP","api.openweathermap.org",80
<<< RESP: ERROR
>>> ERROR: Command failed
```

## 问题诊断

### 如果看不到任何输出
1. 检查硬件连接
2. 检查串口参数设置
3. 检查程序是否正确下载

### 如果看到错误信息
1. 根据错误信息定位问题
2. 检查ESP01S模块状态
3. 检查网络连接

### 如果AT命令失败
1. 检查ESP01S模块电源
2. 检查UART1连接（ESP01S通信）
3. 检查WiFi连接状态

## 注意事项

1. **波特率必须匹配**：确保串口调试助手和程序中的波特率一致
2. **硬件连接正确**：TX接RX，RX接TX
3. **电源稳定**：确保ESP01S模块有稳定的3.3V电源
4. **网络环境**：确保WiFi网络稳定

## 联系支持

如果遇到问题，请提供：
1. 完整的串口调试输出
2. 硬件连接图
3. 具体的错误现象描述 